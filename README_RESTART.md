# Omnilyzer Safe Restart Script

## Overview
The `restart_omnilyzer.sh` script provides a safe and robust way to restart all Omnilyzer services without crashing the server or SSH connections.

## Features
- ✅ **SSH-Safe**: Won't crash your SSH connection
- ✅ **Brutal Port Killing**: Force kills processes on ports 3000 and 3002
- ✅ **Fast Startup**: Optimized startup sequence
- ✅ **Status Monitoring**: Real-time service status checking
- ✅ **Comprehensive Logging**: All activities logged to files
- ✅ **Error Handling**: Graceful error handling and recovery

## Usage

### Basic Restart
```bash
cd /var/www
./restart_omnilyzer.sh
```

### Check Current Status
```bash
# Check running processes
netstat -tlnp | grep -E ":300[0-2]"

# Check logs
tail -f /var/www/logs/omnilyzer-dev.log
tail -f /var/www/logs/omnilyzer-prod.log
```

## What the Script Does

### Step 1: Stop Services
- Brutally kills all processes on port 3000 (Development)
- Brutally kills all processes on port 3002 (Production)
- Waits for ports to be completely free

### Step 2: Start Services
- Starts development server on port 3000
- Starts production server on port 3002
- Uses `nohup` to prevent SSH disconnection issues

### Step 3: Verify Status
- Waits 15 seconds for services to fully start
- Checks service status on each port
- Displays URLs and log file locations

## Service URLs
- **Production**: https://omnilyzer.ai (port 3002)
- **Development**: https://dev.omnilyzer.ai (port 3000)

## Log Files
- **Development**: `/var/www/logs/omnilyzer-dev.log`
- **Production**: `/var/www/logs/omnilyzer-prod.log`
- **Restart Script**: `/var/www/logs/omnilyzer_restart.log`

## Troubleshooting

### Services Not Starting
1. Check log files for errors
2. Verify directories exist:
   - `/var/www/omnilyzer` (development)
   - `/var/www/omnilyzer-prod` (production)
3. Check permissions on log directory: `/var/www/logs`

### Port Already in Use
The script uses `kill -KILL` to forcefully terminate processes, so this should not be an issue.

### SSH Connection Issues
The script uses `nohup` and proper backgrounding to prevent SSH disconnection issues.

## Manual Commands

### Kill Specific Port
```bash
# Find processes on port
lsof -ti:3000

# Kill processes
lsof -ti:3000 | xargs -r kill -KILL
```

### Start Services Manually
```bash
# Development server
cd /var/www/omnilyzer
nohup npm run dev > /var/www/logs/omnilyzer-dev.log 2>&1 &

# Production server
cd /var/www/omnilyzer-prod
nohup npm run dev -- --port 3002 > /var/www/logs/omnilyzer-prod.log 2>&1 &
```

## Safety Features
- Uses `set -euo pipefail` for strict error handling
- Graceful handling of missing processes
- Comprehensive logging for debugging
- Color-coded output for easy reading
- Timeout protection for service startup

## Requirements
- `lsof` command (for port checking)
- `curl` command (for service verification)
- `npm` and Node.js installed
- Proper permissions on `/var/www/logs` directory

## Version
Script Version: 1.0
Last Updated: 2025-07-05
