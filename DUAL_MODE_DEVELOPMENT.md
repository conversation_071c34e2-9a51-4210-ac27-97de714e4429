# Dual-Mode Development Workflow

## 🎯 Core Principle
**ALWAYS develop on the development server first, then deploy to production.**

## 📁 Server Structure
```
/var/www/omnilyzer/          # Development Server (Primary)
/var/www/omnilyzer-prod/     # Production Server (Mirror)
```

## 🔄 Development Workflow

### 1. **Development Phase (Always Start Here)**
- **Primary Location**: `/var/www/omnilyzer/`
- **URL**: https://dev.omnilyzer.ai
- **Ports**: 3000 (Frontend), 3001 (Backend)
- **Environment**: `NODE_ENV=development`

### 2. **Testing Phase**
- Test all changes thoroughly on development server
- Verify both frontend and backend functionality
- Check logs and error handling

### 3. **Production Deployment**
- Copy tested changes to production server
- Update environment configurations
- Restart production services

## 🛠️ Implementation Guidelines

### For AI Assistant (Claude):

#### **Rule 1: Always Start with Development**
```bash
# ALWAYS work in development first
cd /var/www/omnilyzer
# Make changes here
```

#### **Rule 2: Test Before Copying**
```bash
# Test the changes
npm run dev
# Verify functionality
curl -s https://dev.omnilyzer.ai
```

#### **Rule 3: Copy to Production**
```bash
# Copy specific files/directories
cp /var/www/omnilyzer/[file] /var/www/omnilyzer-prod/[file]
# OR sync entire directories
rsync -av /var/www/omnilyzer/components/ /var/www/omnilyzer-prod/components/
```

#### **Rule 4: Environment-Specific Configurations**
- Development: `NODE_ENV=development`
- Production: `NODE_ENV=production`
- Different API endpoints if needed
- Different logging levels

## 📋 Deployment Checklist

### Before Making Changes:
- [ ] Confirm working in `/var/www/omnilyzer/`
- [ ] Check current development server status
- [ ] Backup critical files if needed

### During Development:
- [ ] Make changes in development environment
- [ ] Test functionality thoroughly
- [ ] Check browser console for errors
- [ ] Verify API endpoints work
- [ ] Test chatbot functionality
- [ ] Check frontend logging

### Before Production Deployment:
- [ ] All tests pass on development
- [ ] No console errors
- [ ] All features working as expected
- [ ] Environment variables reviewed

### Production Deployment:
- [ ] Copy changed files to production
- [ ] Update production environment variables
- [ ] Restart production services
- [ ] Test production functionality
- [ ] Verify both servers working

## 🔧 Automated Deployment Script

### Create Deployment Script:
```bash
#!/bin/bash
# /var/www/deploy_to_production.sh

# Sync development to production
rsync -av --exclude='.env' --exclude='node_modules' --exclude='.next' \
  /var/www/omnilyzer/ /var/www/omnilyzer-prod/

# Update production environment
echo "NODE_ENV=production" > /var/www/omnilyzer-prod/.env.production

# Restart production services
cd /var/www && ./restart_omnilyzer.sh
```

## 🌍 Environment-Specific Configurations

### Development Server (`/var/www/omnilyzer/.env`):
```env
NODE_ENV=development
NEXT_PUBLIC_API_URL=https://dev.omnilyzer.ai
FRONTEND_LOG_LEVEL=debug
ENABLE_FRONTEND_LOGGING=true
```

### Production Server (`/var/www/omnilyzer-prod/.env`):
```env
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://omnilyzer.ai
FRONTEND_LOG_LEVEL=error
ENABLE_FRONTEND_LOGGING=true
```

## 🔍 Monitoring & Verification

### Development Monitoring:
```bash
# Check development status
curl -s https://dev.omnilyzer.ai/api/health
tail -f /var/www/logs/omnilyzer-dev.log
```

### Production Monitoring:
```bash
# Check production status
curl -s https://omnilyzer.ai/api/health
tail -f /var/www/logs/omnilyzer-prod.log
```

## 🚨 Emergency Procedures

### If Production Breaks:
1. **Immediate**: Revert to last known good state
2. **Investigate**: Check development server for issues
3. **Fix**: Implement fix on development first
4. **Test**: Thoroughly test on development
5. **Deploy**: Copy fix to production

### Rollback Command:
```bash
# Keep backups for quick rollback
cp -r /var/www/omnilyzer-prod /var/www/omnilyzer-prod.backup.$(date +%Y%m%d_%H%M%S)
```

## 📝 File Synchronization Strategy

### Critical Files to Always Sync:
- `components/` - React components
- `lib/` - Utility libraries
- `utils/` - Helper functions
- `app/` - Next.js app directory
- `public/` - Static assets

### Environment-Specific Files (Don't Sync):
- `.env` - Environment variables
- `node_modules/` - Dependencies
- `.next/` - Build artifacts
- `logs/` - Log files

### Selective Sync Commands:
```bash
# Sync specific directories
rsync -av /var/www/omnilyzer/components/ /var/www/omnilyzer-prod/components/
rsync -av /var/www/omnilyzer/lib/ /var/www/omnilyzer-prod/lib/
rsync -av /var/www/omnilyzer/app/ /var/www/omnilyzer-prod/app/

# Sync package.json but not node_modules
cp /var/www/omnilyzer/package.json /var/www/omnilyzer-prod/
cd /var/www/omnilyzer-prod && npm install
```

## 🎯 Best Practices Summary

1. **Development First**: Always start changes in `/var/www/omnilyzer/`
2. **Test Thoroughly**: Verify all functionality before production
3. **Environment Awareness**: Use appropriate configs for each environment
4. **Incremental Deployment**: Copy specific files rather than entire directories
5. **Monitor Both**: Keep tabs on both development and production
6. **Document Changes**: Track what was changed and why
7. **Backup Strategy**: Keep backups before major changes
8. **Rollback Plan**: Always have a way to revert changes

This workflow ensures consistency, reduces errors, and maintains a clear development → production pipeline.
