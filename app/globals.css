/* Temporarily removed Tailwind directives to test CSS processing */

/* Base styles */
:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 245, 245, 245;
  --primary-color: 21, 101, 192;
  --secondary-color: 66, 66, 66;
  --accent-color: 0, 150, 136;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(to bottom,
      transparent,
      rgb(var(--background-end-rgb))) rgb(var(--background-start-rgb));
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}

/* Utility classes to replace common Tailwind classes */
.container {
  padding: 1rem;
  margin: 0 auto;
  max-width: 1200px;
}

.btn-primary {
  background-color: #1d4ed8;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-primary:hover {
  background-color: #1e40af;
}

.text-center {
  text-align: center;
}

.mt-4 {
  margin-top: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.p-4 {
  padding: 1rem;
}

.flex {
  display: flex;
}

.justify-center {
  justify-content: center;
}

.items-center {
  align-items: center;
}

.min-h-screen {
  min-height: 100vh;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 245, 245, 245;
  --primary-color: 21, 101, 192;
  --secondary-color: 66, 66, 66;
  --accent-color: 0, 150, 136;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(to bottom,
      transparent,
      rgb(var(--background-end-rgb))) rgb(var(--background-start-rgb));
}

@layer components {
  .container {
    @apply px-4 md:px-6 mx-auto max-w-6xl;
  }

  .btn-primary {
    @apply bg-blue-700 hover:bg-blue-800 text-white font-medium py-2 px-6 rounded-md transition-colors duration-300;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-6 rounded-md transition-colors duration-300;
  }

  .heading-1 {
    @apply text-4xl md:text-5xl font-bold tracking-tight;
  }

  .heading-2 {
    @apply text-3xl md:text-4xl font-bold;
  }

  .heading-3 {
    @apply text-2xl md:text-3xl font-semibold;
  }

  .section {
    @apply py-12 md:py-20;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6 transition-shadow duration-300 hover:shadow-lg;
  }

  /* Service icons constraint */
  .service-icon {
    @apply h-8 w-8 text-blue-700 mx-auto;
  }

  /* Image constraints */
  .img-constrained {
    @apply object-contain max-h-[400px] w-auto mx-auto;
  }

  /* Arrow icon constraints */
  .arrow-icon {
    @apply h-4 w-4 inline-block;
  }

  /* Service icon container */
  .icon-container {
    @apply h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mb-6 mx-auto md:mx-0;
  }

  /* Enhanced micro-interactions */
  .hover-lift {
    @apply transition-all duration-300 ease-out;
  }

  .hover-lift:hover {
    @apply transform -translate-y-1 shadow-lg;
  }

  .hover-glow {
    @apply transition-all duration-300 ease-out;
  }

  .hover-glow:hover {
    @apply shadow-lg shadow-blue-500/25;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent;
  }

  /* Enhanced button styles */
  .btn-enhanced {
    @apply relative overflow-hidden transition-all duration-300 ease-out;
  }

  .btn-enhanced::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 transform -skew-x-12 -translate-x-full transition-all duration-700;
  }

  .btn-enhanced:hover::before {
    @apply opacity-20 translate-x-full;
  }
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-blue-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-blue-600;
}