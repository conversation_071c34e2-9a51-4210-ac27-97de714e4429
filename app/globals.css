@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 245, 245, 245;
  --primary-color: 21, 101, 192;
  --secondary-color: 66, 66, 66;
  --accent-color: 0, 150, 136;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(to bottom,
      transparent,
      rgb(var(--background-end-rgb))) rgb(var(--background-start-rgb));
}

/* Component styles converted from Tailwind */
.container {
  padding-left: 1rem;
  padding-right: 1rem;
  margin-left: auto;
  margin-right: auto;
  max-width: 72rem;
}

@media (min-width: 768px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

.btn-primary {
  background-color: #1d4ed8;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-primary:hover {
  background-color: #1e40af;
}

.btn-secondary {
  background-color: #e5e7eb;
  color: #1f2937;
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-secondary:hover {
  background-color: #d1d5db;
}

.heading-1 {
  font-size: 2.25rem;
  font-weight: 700;
  letter-spacing: -0.025em;
}

@media (min-width: 768px) {
  .heading-1 {
    font-size: 3rem;
  }
}

.heading-2 {
  font-size: 1.875rem;
  font-weight: 700;
}

@media (min-width: 768px) {
  .heading-2 {
    font-size: 2.25rem;
  }
}

.heading-3 {
  font-size: 1.5rem;
  font-weight: 600;
}

@media (min-width: 768px) {
  .heading-3 {
    font-size: 1.875rem;
  }
}

.section {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

@media (min-width: 768px) {
  .section {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: box-shadow 0.3s;
}

.card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Service icons constraint */
.service-icon {
  height: 2rem;
  width: 2rem;
  color: #1d4ed8;
  margin-left: auto;
  margin-right: auto;
}

/* Image constraints */
.img-constrained {
  object-fit: contain;
  max-height: 400px;
  width: auto;
  margin-left: auto;
  margin-right: auto;
}

/* Arrow icon constraints */
.arrow-icon {
  height: 1rem;
  width: 1rem;
  display: inline-block;
}

/* Service icon container */
.icon-container {
  height: 4rem;
  width: 4rem;
  background-color: #dbeafe;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 768px) {
  .icon-container {
    margin-left: 0;
    margin-right: 0;
  }
}

/* Enhanced micro-interactions */
.hover-lift {
  transition: all 0.3s ease-out;
}

.hover-lift:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.hover-glow {
  transition: all 0.3s ease-out;
}

.hover-glow:hover {
  box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.25);
}

.text-gradient {
  background: linear-gradient(to right, #2563eb, #1e40af);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* Enhanced button styles */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease-out;
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(to right, transparent, white, transparent);
  opacity: 0;
  transform: skewX(-12deg) translateX(-100%);
  transition: all 0.7s;
}

.btn-enhanced:hover::before {
  opacity: 0.2;
  transform: skewX(-12deg) translateX(100%);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background-color: #f3f4f6;
}

::-webkit-scrollbar-thumb {
  background-color: #60a5fa;
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #2563eb;
}