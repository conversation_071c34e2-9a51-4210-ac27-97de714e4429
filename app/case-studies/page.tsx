import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Case Studies - Omnilyzer',
  description: 'Real success stories from businesses that transformed their operations with Omnilyzer AI solutions.',
  keywords: 'AI case studies, automation success stories, business transformation, ROI examples',
};

export default function CaseStudiesPage() {
  const caseStudies = [
    {
      id: 1,
      title: 'Soccer Training Academy: Online Appointment Calendar',
      client: 'Professional Soccer Training Academy',
      industry: 'Sports & Education',
      challenge: 'Manual scheduling was consuming 15+ hours per week and causing booking conflicts',
      solution: 'Custom online appointment calendar with automated notifications and payment integration',
      results: [
        'Reduced scheduling time by massive reduction',
        'Eliminated double bookings completely',
        'Increased revenue through better time utilization',
        'Improved customer satisfaction scores'
      ],
      technologies: ['React', 'Node.js', 'Calendar API', 'Payment Gateway'],
      timeline: '6 weeks',
      image: '/images/case-studies/soccer-academy.jpg'
    },
    {
      id: 2,
      title: 'Mid-Size Company: AI-Powered Employee Assessment',
      client: 'Mid-Size Technology Company',
      industry: 'Technology Services',
      challenge: 'Time-consuming manual employee evaluations and inconsistent assessment criteria',
      solution: 'AI-powered assessment tool with automated scoring and detailed analytics',
      results: [
        'Reduced assessment time significantly',
        'Improved assessment consistency substantially',
        'Enhanced employee development tracking',
        'Better data-driven HR decisions'
      ],
      technologies: ['AI/ML', 'Python', 'Data Analytics', 'Dashboard'],
      timeline: '8 weeks',
      image: '/images/case-studies/employee-assessment.jpg'
    },
    {
      id: 3,
      title: 'Manufacturing Company: Process Automation',
      client: 'Industrial Manufacturing Company',
      industry: 'Manufacturing',
      challenge: 'Manual quality control processes leading to inconsistent results and delays',
      solution: 'Automated quality control system with real-time monitoring and alerts',
      results: [
        'Reduced quality control time substantially',
        'Improved accuracy significantly',
        'Decreased production delays dramatically',
        'Enhanced compliance reporting'
      ],
      technologies: ['IoT Sensors', 'Machine Learning', 'Real-time Analytics'],
      timeline: '12 weeks',
      image: '/images/case-studies/manufacturing.jpg'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Success Stories
            </h1>
            <p className="text-xl text-blue-100">
              Real businesses, real results. See how our AI solutions have transformed operations and delivered measurable value.
            </p>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-white py-12 border-b">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">50+</div>
              <div className="text-gray-600">Projects Completed</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">95%</div>
              <div className="text-gray-600">Client Satisfaction</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">Significant</div>
              <div className="text-gray-600">Average Efficiency Gain</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">6 months</div>
              <div className="text-gray-600">Average ROI Timeline</div>
            </div>
          </div>
        </div>
      </div>

      {/* Case Studies */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="space-y-16">
          {caseStudies.map((study, index) => (
            <div key={study.id} className={`flex flex-col ${index % 2 === 1 ? 'lg:flex-row-reverse' : 'lg:flex-row'} gap-8 items-center`}>
              {/* Image */}
              <div className="lg:w-1/2">
                <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg h-64 lg:h-80 flex items-center justify-center text-white">
                  <div className="text-center">
                    <div className="text-6xl mb-4">
                      {study.industry === 'Sports & Education' ? '⚽' :
                        study.industry === 'Technology Services' ? '💼' : '🏭'}
                    </div>
                    <div className="text-lg font-medium">{study.industry}</div>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="lg:w-1/2">
                <div className="bg-white rounded-lg shadow-lg p-8">
                  <div className="mb-4">
                    <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                      {study.industry}
                    </span>
                  </div>

                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    {study.title}
                  </h2>

                  <div className="space-y-4 mb-6">
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Challenge</h3>
                      <p className="text-gray-600">{study.challenge}</p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Solution</h3>
                      <p className="text-gray-600">{study.solution}</p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Results</h3>
                      <ul className="space-y-1">
                        {study.results.map((result, idx) => (
                          <li key={idx} className="flex items-center text-gray-600">
                            <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                            {result}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                    <div>
                      <span className="font-medium">Timeline:</span> {study.timeline}
                    </div>
                    <div>
                      <span className="font-medium">Technologies:</span> {study.technologies.join(', ')}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-blue-600 text-white py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Write Your Success Story?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join the growing list of businesses that have transformed their operations with our AI solutions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="#contact"
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200"
            >
              Start Your Project
            </a>
            <a
              href="/contact"
              className="border border-white text-white px-8 py-3 rounded-lg font-medium hover:bg-white hover:text-blue-600 transition-colors duration-200"
            >
              Schedule Consultation
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
