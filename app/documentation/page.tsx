import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Documentation - Omnilyzer',
  description: 'Complete documentation for Omnilyzer AI solutions, APIs, and integration guides.',
  keywords: 'API documentation, integration guides, AI solutions documentation, developer resources',
};

export default function DocumentationPage() {
  const docSections = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      description: 'Quick start guide to begin using Omnilyzer solutions',
      icon: '🚀',
      items: [
        'Account Setup',
        'First Project',
        'Basic Configuration',
        'Initial Integration'
      ]
    },
    {
      id: 'api-reference',
      title: 'API Reference',
      description: 'Complete API documentation with examples',
      icon: '📚',
      items: [
        'Authentication',
        'Endpoints',
        'Request/Response Format',
        'Error Handling'
      ]
    },
    {
      id: 'integrations',
      title: 'Integrations',
      description: 'Connect Omnilyzer with your existing systems',
      icon: '🔗',
      items: [
        'CRM Integration',
        'Database Connections',
        'Third-party APIs',
        'Webhook Configuration'
      ]
    },
    {
      id: 'ai-solutions',
      title: 'AI Solutions',
      description: 'Detailed guides for our AI-powered features',
      icon: '🤖',
      items: [
        'Chatbot Setup',
        'Data Analysis',
        'Automation Workflows',
        'Custom Models'
      ]
    },
    {
      id: 'troubleshooting',
      title: 'Troubleshooting',
      description: 'Common issues and their solutions',
      icon: '🔧',
      items: [
        'Common Errors',
        'Performance Issues',
        'Configuration Problems',
        'Debug Mode'
      ]
    },
    {
      id: 'best-practices',
      title: 'Best Practices',
      description: 'Recommended approaches for optimal results',
      icon: '⭐',
      items: [
        'Security Guidelines',
        'Performance Optimization',
        'Data Management',
        'Monitoring & Alerts'
      ]
    }
  ];

  const quickLinks = [
    { title: 'API Keys', href: '#api-keys' },
    { title: 'Rate Limits', href: '#rate-limits' },
    { title: 'SDKs', href: '#sdks' },
    { title: 'Webhooks', href: '#webhooks' },
    { title: 'Status Page', href: '#status' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Documentation
            </h1>
            <p className="text-xl text-blue-100">
              Everything you need to integrate and maximize the value of Omnilyzer AI solutions in your business.
            </p>
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="bg-white border-b py-8">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <input
                type="text"
                placeholder="Search documentation..."
                className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-1/4">
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Quick Links</h3>
              <nav className="space-y-2">
                {quickLinks.map((link) => (
                  <a
                    key={link.title}
                    href={link.href}
                    className="block text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-3 py-2 rounded-lg transition-colors duration-200"
                  >
                    {link.title}
                  </a>
                ))}
              </nav>
              
              <div className="mt-8 pt-6 border-t">
                <h4 className="font-medium text-gray-900 mb-3">Need Help?</h4>
                <div className="space-y-2 text-sm">
                  <a href="/support" className="block text-blue-600 hover:text-blue-800">
                    Contact Support
                  </a>
                  <a href="/case-studies" className="block text-blue-600 hover:text-blue-800">
                    View Examples
                  </a>
                  <a href="/blog" className="block text-blue-600 hover:text-blue-800">
                    Read Tutorials
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Main Documentation */}
          <div className="lg:w-3/4">
            {/* Documentation Sections Grid */}
            <div className="grid md:grid-cols-2 gap-6 mb-12">
              {docSections.map((section) => (
                <div key={section.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
                  <div className="flex items-center mb-4">
                    <div className="text-3xl mr-4">{section.icon}</div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">{section.title}</h3>
                      <p className="text-gray-600 text-sm">{section.description}</p>
                    </div>
                  </div>
                  <ul className="space-y-2">
                    {section.items.map((item) => (
                      <li key={item} className="flex items-center text-gray-600">
                        <svg className="w-4 h-4 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                        </svg>
                        <a href={`#${item.toLowerCase().replace(/\s+/g, '-')}`} className="hover:text-blue-600">
                          {item}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>

            {/* Featured Documentation */}
            <div className="bg-white rounded-lg shadow-md p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Getting Started with Omnilyzer</h2>
              
              <div className="prose max-w-none">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Quick Setup Guide</h3>
                <p className="text-gray-600 mb-4">
                  Follow these steps to get started with Omnilyzer AI solutions in your business environment.
                </p>
                
                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">Step 1: Account Configuration</h4>
                  <div className="bg-gray-800 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                    <code>
                      {`// Initialize Omnilyzer client
const omnilyzer = new OmnilyzerClient({
  apiKey: 'your-api-key',
  environment: 'production'
});`}
                    </code>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">Step 2: Basic Integration</h4>
                  <div className="bg-gray-800 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                    <code>
                      {`// Make your first API call
const response = await omnilyzer.analyze({
  data: yourData,
  type: 'business-intelligence'
});`}
                    </code>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <div className="flex items-start">
                    <div className="text-blue-600 mr-3 mt-1">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium text-blue-900 mb-1">Pro Tip</h4>
                      <p className="text-blue-800 text-sm">
                        Start with our sandbox environment to test integrations before moving to production.
                      </p>
                    </div>
                  </div>
                </div>

                <h3 className="text-lg font-semibold text-gray-900 mb-3">Next Steps</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Explore our API Reference for detailed endpoint documentation
                  </li>
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Check out our Case Studies for real-world implementation examples
                  </li>
                  <li className="flex items-center">
                    <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Join our developer community for support and best practices
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
