import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Support - Omnilyzer',
  description: 'Get help with Omnilyzer AI solutions. Contact our support team, browse FAQs, and access resources.',
  keywords: 'technical support, customer service, help center, FAQ, troubleshooting',
};

export default function SupportPage() {
  const supportOptions = [
    {
      id: 'contact',
      title: 'Contact Support',
      description: 'Get direct help from our technical support team',
      icon: '📧',
      action: 'Send Message',
      href: '/contact'
    },
    {
      id: 'documentation',
      title: 'Documentation',
      description: 'Browse our comprehensive guides and API reference',
      icon: '📚',
      action: 'View Docs',
      href: '/documentation'
    },
    {
      id: 'community',
      title: 'Community Forum',
      description: 'Connect with other users and share experiences',
      icon: '👥',
      action: 'Join Forum',
      href: '#community'
    },
    {
      id: 'status',
      title: 'System Status',
      description: 'Check current system status and uptime',
      icon: '🟢',
      action: 'View Status',
      href: '#status'
    }
  ];

  const faqs = [
    {
      question: 'How do I get started with Omnily<PERSON>?',
      answer: 'Getting started is easy! Contact us through our contact form to schedule a consultation. We\'ll assess your needs and create a custom implementation plan for your business.'
    },
    {
      question: 'What kind of support do you provide?',
      answer: 'We provide comprehensive support including initial setup, training, ongoing technical support, and regular check-ins to ensure optimal performance of your AI solutions.'
    },
    {
      question: 'How long does implementation typically take?',
      answer: 'Implementation timelines vary based on project complexity. Simple integrations can be completed in 2-4 weeks, while comprehensive AI solutions may take 8-12 weeks. We provide detailed timelines during consultation.'
    },
    {
      question: 'Do you provide training for our team?',
      answer: 'Yes! We include comprehensive training as part of our implementation process. This covers system usage, best practices, and ongoing maintenance to ensure your team can maximize the value of your AI solutions.'
    },
    {
      question: 'What if we need customizations?',
      answer: 'We specialize in custom AI solutions tailored to your specific business needs. Our team will work with you to understand your requirements and develop solutions that fit perfectly with your existing workflows.'
    },
    {
      question: 'How do you ensure data security?',
      answer: 'Data security is our top priority. We implement enterprise-grade security measures, including encryption, secure APIs, and compliance with industry standards to protect your sensitive business data.'
    }
  ];

  const contactInfo = {
    phone: {
      denmark: '+45 3131 3355',
      philippines: '+63 ************'
    },
    email: '<EMAIL>',
    hours: '9 AM - 4 PM CET',
    offices: {
      denmark: 'Lystruplund 37, 8520 Lustrup, Denmark',
      philippines: '19 L. R. Aguinaldo str., Circulo Village, 1700 Paranque, Philippines'
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Support Center
            </h1>
            <p className="text-xl text-blue-100">
              We're here to help you succeed with Omnilyzer AI solutions. Get the support you need, when you need it.
            </p>
          </div>
        </div>
      </div>

      {/* Support Options */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {supportOptions.map((option) => (
            <div key={option.id} className="bg-white rounded-lg shadow-md p-6 text-center hover:shadow-lg transition-shadow duration-300">
              <div className="text-4xl mb-4">{option.icon}</div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">{option.title}</h3>
              <p className="text-gray-600 mb-4 text-sm">{option.description}</p>
              <a
                href={option.href}
                className="inline-block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm"
              >
                {option.action}
              </a>
            </div>
          ))}
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* FAQ Section */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Frequently Asked Questions</h2>
            <div className="space-y-6">
              {faqs.map((faq, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">{faq.question}</h3>
                  <p className="text-gray-600">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Contact Information */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Contact Information</h2>
            
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Get in Touch</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <span className="text-blue-600">📧</span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">Email</div>
                    <div className="text-gray-600">{contactInfo.email}</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <span className="text-blue-600">🕒</span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">Business Hours</div>
                    <div className="text-gray-600">{contactInfo.hours}</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Phone Numbers</h3>
              <div className="space-y-3">
                <div>
                  <div className="font-medium text-gray-900">Denmark</div>
                  <div className="text-gray-600">{contactInfo.phone.denmark}</div>
                </div>
                <div>
                  <div className="font-medium text-gray-900">Philippines</div>
                  <div className="text-gray-600">{contactInfo.phone.philippines}</div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Office Locations</h3>
              <div className="space-y-4">
                <div>
                  <div className="font-medium text-gray-900">Denmark Office</div>
                  <div className="text-gray-600 text-sm">{contactInfo.offices.denmark}</div>
                </div>
                <div>
                  <div className="font-medium text-gray-900">Philippines Office</div>
                  <div className="text-gray-600 text-sm">{contactInfo.offices.philippines}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-blue-600 text-white py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Still Need Help?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Our support team is ready to assist you with any questions or technical issues you may have.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200"
            >
              Contact Support
            </a>
            <a
              href="/documentation"
              className="border border-white text-white px-8 py-3 rounded-lg font-medium hover:bg-white hover:text-blue-600 transition-colors duration-200"
            >
              Browse Documentation
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
