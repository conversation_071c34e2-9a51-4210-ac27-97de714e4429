'use client';

import Image from 'next/image';
import Link from 'next/link';
import ScrollAnimation from '../components/ScrollAnimation';
import BackToTop from '../components/BackToTop';
import ContactSection from '../components/ContactSection';

// Force dynamic rendering for this page


export default function Home() {
  return (
    <>
      <main className="min-h-screen">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-blue-700 to-blue-900 text-white py-20 md:py-28">
          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
              <div>
                <ScrollAnimation animation="fadeInUp" delay={200}>
                  <h1 className="heading-1 mb-6">
                    Custom Web & AI Solutions for <span className="text-blue-200">B2B Growth</span>
                  </h1>
                </ScrollAnimation>
                <ScrollAnimation animation="fadeInUp" delay={400}>
                  <p className="text-lg md:text-xl mb-8 opacity-90">
                    Omnilyzer helps industrial and B2B companies streamline operations with tailored web applications and AI-powered tools.
                    From HR portals and time-tracking systems to intelligent chatbots, get solutions built for efficiency and ROI.
                  </p>
                </ScrollAnimation>
                <ScrollAnimation animation="fadeInUp" delay={600}>
                  <div className="flex flex-wrap gap-4">
                    <a
                      href="#contact"
                      className="bg-white text-blue-900 hover:bg-gray-100 font-medium py-3 px-8 rounded-md transition-colors duration-300 inline-flex items-center hover:scale-105 transform cursor-pointer"
                      onClick={(e) => {
                        e.preventDefault();
                        document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' });
                      }}
                    >
                      Get a Free Consultation
                      <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </a>
                    <Link href="/services" className="bg-transparent hover:bg-blue-800 border border-white text-white font-medium py-3 px-8 rounded-md transition-colors duration-300 hover:scale-105 transform">
                      Explore Services
                    </Link>
                  </div>
                </ScrollAnimation>
              </div>
              <div className="hidden md:block">
                <ScrollAnimation animation="fadeInRight" delay={300}>
                  <Image
                    src="/images/hero-image.jpg"
                    alt="AI Technology Visualization"
                    width={600}
                    height={400}
                    className="rounded-lg img-constrained hover:scale-105 transition-transform duration-300"
                    priority
                  />
                </ScrollAnimation>
              </div>
            </div>
          </div>
        </section>

        {/* Services Overview Section */}
        <section className="section bg-white">
          <div className="container">
            <h2 className="heading-2 text-center mb-12">Services</h2>
            <div className="flex flex-wrap justify-center gap-8">
              {/* Service 1 */}
              <ScrollAnimation animation="fadeInUp" delay={200}>
                <div className="card hover:shadow-lg transition-shadow duration-300 h-full flex flex-col text-center w-full max-w-sm">
                  <div className="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" className="service-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-3">B2B & Industrial Web Applications</h3>
                  <p className="text-gray-600 mb-4 flex-grow">
                    Build custom portals and internal tools — employee assessment, time tracking, expense management, and more — designed to automate your workflows and eliminate spreadsheets.
                  </p>
                  <Link href="/services/b2b-applications" className="text-blue-700 hover:text-blue-900 font-medium inline-flex items-center mt-auto">
                    Learn More
                    <svg xmlns="http://www.w3.org/2000/svg" className="arrow-icon ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </ScrollAnimation>

              {/* Service 2 */}
              <ScrollAnimation animation="fadeInUp" delay={400}>
                <div className="card hover:shadow-lg transition-shadow duration-300 h-full flex flex-col text-center w-full max-w-sm">
                  <div className="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" className="service-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-3">AI Chatbots & Automation</h3>
                  <p className="text-gray-600 mb-4 flex-grow">
                    Deploy AI-powered virtual assistants for customer support or internal helpdesks. Available 24/7 to answer FAQs, route requests, and capture leads without adding headcount.
                  </p>
                  <Link href="/services/ai-chatbots" className="text-blue-700 hover:text-blue-900 font-medium inline-flex items-center mt-auto">
                    Learn More
                    <svg xmlns="http://www.w3.org/2000/svg" className="arrow-icon ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </ScrollAnimation>

              {/* Service 3 */}
              <ScrollAnimation animation="fadeInUp" delay={600}>
                <div className="card hover:shadow-lg transition-shadow duration-300 h-full flex flex-col text-center w-full max-w-sm">
                  <div className="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" className="service-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-3">SaaS & Startup Platforms</h3>
                  <p className="text-gray-600 mb-4 flex-grow">
                    Launch scalable web applications and MVPs faster. Clean, modular codebases that grow with your business and appeal to investors.
                  </p>
                  <Link href="/services/saas-platforms" className="text-blue-700 hover:text-blue-900 font-medium inline-flex items-center mt-auto">
                    Learn More
                    <svg xmlns="http://www.w3.org/2000/svg" className="arrow-icon ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </ScrollAnimation>

              {/* Service 4 */}
              <ScrollAnimation animation="fadeInUp" delay={800}>
                <div className="card hover:shadow-lg transition-shadow duration-300 h-full flex flex-col text-center w-full max-w-sm">
                  <div className="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" className="service-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-3">SEO & Content Management</h3>
                  <p className="text-gray-600 mb-4 flex-grow">
                    Get a search-optimized site plus an easy-to-use CMS. Publish blogs or product updates yourself—or leave it to me with a monthly content package.
                  </p>
                  <Link href="/services/seo-cms" className="text-blue-700 hover:text-blue-900 font-medium inline-flex items-center mt-auto">
                    Learn More
                    <svg xmlns="http://www.w3.org/2000/svg" className="arrow-icon ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </ScrollAnimation>

              {/* Service 5 */}
              <ScrollAnimation animation="fadeInUp" delay={1000}>
                <div className="card hover:shadow-lg transition-shadow duration-300 h-full flex flex-col text-center w-full max-w-sm">
                  <div className="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" className="service-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-3">Hosting & Maintenance</h3>
                  <p className="text-gray-600 mb-4 flex-grow">
                    Fast, secure hosting with regular updates, backups, and security patches—all included so you never worry about downtime.
                  </p>
                  <Link href="/services/hosting-maintenance" className="text-blue-700 hover:text-blue-900 font-medium inline-flex items-center mt-auto">
                    Learn More
                    <svg xmlns="http://www.w3.org/2000/svg" className="arrow-icon ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </ScrollAnimation>
            </div>
          </div>
        </section>

        {/* Why Choose Omnilyzer Section */}
        <section className="section bg-blue-50">
          <div className="container">
            <div className="text-center mb-12">
              <ScrollAnimation animation="fadeInUp" delay={200}>
                <h2 className="heading-2 mb-4">Why Choose Omnilyzer?</h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  A brief overview of what makes us different in the B2B and industrial space
                </p>
              </ScrollAnimation>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 justify-items-center">
              {/* Reason 1 */}
              <ScrollAnimation animation="fadeInUp" delay={300}>
                <div className="bg-white p-6 rounded-lg shadow-md hover-lift hover-glow text-center h-full flex flex-col">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold mb-3">B2B Focus</h3>
                  <p className="text-gray-600 text-sm flex-grow">
                    I speak your industry's language and solve real business problems—no one-size-fits-all templates.
                  </p>
                </div>
              </ScrollAnimation>

              {/* Reason 2 */}
              <ScrollAnimation animation="fadeInUp" delay={500}>
                <div className="bg-white p-6 rounded-lg shadow-md hover-lift hover-glow text-center h-full flex flex-col">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold mb-3">AI Expertise</h3>
                  <p className="text-gray-600 text-sm flex-grow">
                    From chatbots to data-driven dashboards, I integrate AI only where it drives clear value.
                  </p>
                </div>
              </ScrollAnimation>

              {/* Reason 3 */}
              <ScrollAnimation animation="fadeInUp" delay={700}>
                <div className="bg-white p-6 rounded-lg shadow-md hover-lift hover-glow text-center h-full flex flex-col">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-purple-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold mb-3">Productized & Custom</h3>
                  <p className="text-gray-600 text-sm flex-grow">
                    Core tools you can subscribe to, plus tailor-made enhancements for your exact needs.
                  </p>
                </div>
              </ScrollAnimation>

              {/* Reason 4 */}
              <ScrollAnimation animation="fadeInUp" delay={900}>
                <div className="bg-white p-6 rounded-lg shadow-md hover-lift hover-glow text-center h-full flex flex-col">
                  <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-yellow-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold mb-3">Predictable Pricing</h3>
                  <p className="text-gray-600 text-sm flex-grow">
                    Clear project or subscription fees—no surprises, just steady ROI.
                  </p>
                </div>
              </ScrollAnimation>
            </div>
          </div>
        </section>

        {/* Who I Am */}
        <section className="section bg-gray-50">
          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="heading-2 mb-6">Who I Am</h2>
                <p className="text-gray-700 mb-6">
                  I'm Michael Hansen, a Danish-born AI developer. With a solid background in IT service management and cloud operations,
                  I've turned my passion for efficiency into crafting web and AI solutions for B2B and industrial clients worldwide.
                </p>
                <p className="text-gray-700 mb-6">
                  My mission is to make powerful, enterprise-grade tools accessible to small and medium businesses through both one-off projects
                  and scalable SaaS offerings.
                </p>
                <Link href="/about" className="btn-primary">
                  More About Me
                </Link>
              </div>
              <div className="md:order-first">
                <Image
                  src="/images/about-image.jpg"
                  alt="Michael Trust Hansen"
                  width={600}
                  height={400}
                  className="rounded-lg shadow-lg img-constrained"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <ContactSection />

        {/* CTA Section */}
        <section className="section bg-blue-700 text-white">
          <div className="container text-center">
            <h2 className="heading-2 mb-6">Ready to Solve Your B2B Challenges?</h2>
            <p className="text-lg md:text-xl mb-8 max-w-3xl mx-auto opacity-90">
              Get a free 30-minute strategy session—no obligation. We'll review your current processes, identify high-impact opportunities,
              and map out a custom web/AI roadmap that saves you time and money.
            </p>
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              <Link href="/contact" className="bg-white text-blue-900 hover:bg-gray-100 font-medium py-4 px-8 rounded-md transition-colors duration-300 inline-flex items-center text-lg">
                Book Your Free Consultation
                <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </div>

            {/* What You'll Get */}
            <div className="bg-blue-800 rounded-lg p-6 max-w-4xl mx-auto">
              <h3 className="text-xl font-bold mb-4">What You'll Get (Free Session)</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-blue-200 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-blue-100">In-depth review of your current workflows and pain points</span>
                </div>
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-blue-200 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-blue-100">Tailored solution outline and implementation timeline</span>
                </div>
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-blue-200 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-blue-100">Estimated ROI and efficiency gains</span>
                </div>
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-blue-200 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-blue-100">Clear next steps—no obligation</span>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <BackToTop />
    </>
  );
}
