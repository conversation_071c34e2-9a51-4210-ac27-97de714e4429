import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    // Generate security audit data
    const securityData = {
      metrics: {
        securityScore: 87.5,
        vulnerabilities: {
          critical: 0,
          high: 1,
          medium: 3,
          low: 7,
          info: 12
        },
        lastScan: new Date().toISOString(),
        nextScan: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      },
      recentEvents: [
        {
          type: 'login_success',
          user: '<EMAIL>',
          ip: '*************',
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          severity: 'info'
        },
        {
          type: 'failed_login',
          user: '<EMAIL>',
          ip: '************',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          severity: 'warning'
        },
        {
          type: 'rate_limit_exceeded',
          ip: '*************',
          endpoint: '/api/v1/auth/login',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          severity: 'warning'
        },
        {
          type: 'ssl_certificate_check',
          domain: 'omnilyzer.ai',
          status: 'valid',
          expiresAt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
          timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
          severity: 'info'
        }
      ],
      scan: {
        status: 'completed',
        startTime: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
        endTime: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        duration: 300, // seconds
        findings: [
          {
            type: 'high',
            category: 'Authentication',
            title: 'Weak Password Policy',
            description: 'Password policy could be strengthened with additional complexity requirements',
            recommendation: 'Implement stronger password requirements including special characters and minimum length of 12',
            affected: ['User Registration', 'Password Reset']
          },
          {
            type: 'medium',
            category: 'Headers',
            title: 'Missing Security Headers',
            description: 'Some security headers are not configured',
            recommendation: 'Add Content-Security-Policy and X-Frame-Options headers',
            affected: ['All HTTP responses']
          },
          {
            type: 'medium',
            category: 'SSL/TLS',
            title: 'TLS Configuration',
            description: 'TLS configuration could be optimized',
            recommendation: 'Disable older TLS versions and weak cipher suites',
            affected: ['HTTPS connections']
          },
          {
            type: 'low',
            category: 'Information Disclosure',
            title: 'Server Version Disclosure',
            description: 'Server version information is exposed in headers',
            recommendation: 'Configure server to hide version information',
            affected: ['HTTP headers']
          }
        ],
        compliance: {
          gdpr: 85,
          iso27001: 78,
          owasp: 82
        }
      },
      generatedAt: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      data: securityData
    });

  } catch (error) {
    console.error('Security audit error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get security audit data' },
      { status: 500 }
    );
  }
}
