import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

// File-based storage for demo purposes
const DATA_FILE = path.join(process.cwd(), 'data', 'auth-users.json');

async function readUsers() {
  try {
    const data = await fs.readFile(DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.users;
  } catch {
    return [];
  }
}

async function writeUsers(users: any[]) {
  const dataDir = path.dirname(DATA_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
  await fs.writeFile(DATA_FILE, JSON.stringify({ users, lastUpdated: new Date().toISOString() }, null, 2));
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const userId = parseInt(params.userId);
    const body = await request.json();

    // Read current users
    const users = await readUsers();

    // Find the user to update
    const userIndex = users.findIndex(user => user.id === userId);
    if (userIndex === -1) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Update the user
    users[userIndex] = {
      ...users[userIndex],
      ...body,
      updatedAt: new Date().toISOString()
    };

    // Save back to file
    await writeUsers(users);

    return NextResponse.json({
      success: true,
      message: 'User updated successfully',
      user: users[userIndex]
    });

  } catch (error) {
    console.error('Update auth user error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update user' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const userId = parseInt(params.userId);

    // Read current users
    const users = await readUsers();

    // Find the user to delete
    const userIndex = users.findIndex(user => user.id === userId);
    if (userIndex === -1) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Check if this is the last admin (prevent lockout)
    const user = users[userIndex];
    if (user.role === 'Admin') {
      const adminCount = users.filter(u => u.role === 'Admin').length;
      if (adminCount <= 1) {
        return NextResponse.json(
          { success: false, message: 'Cannot delete the last admin user' },
          { status: 400 }
        );
      }
    }

    // Remove the user
    users.splice(userIndex, 1);

    // Save back to file
    await writeUsers(users);

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Delete auth user error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete user' },
      { status: 500 }
    );
  }
}
