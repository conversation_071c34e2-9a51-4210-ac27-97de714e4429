import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

// File-based storage for demo purposes
// In production, this would be replaced with a proper database
const DATA_FILE = path.join(process.cwd(), 'data', 'auth-users.json');

const defaultUsers = [
  {
    id: 1,
    email: '<EMAIL>',
    name: 'System Administrator',
    role: 'Admin',
    status: 'active',
    lastLogin: '2025-07-05T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2025-07-05T10:30:00Z',
    loginAttempts: 0,
    emailVerified: true,
    twoFactorEnabled: true
  },
  {
    id: 2,
    email: '<EMAIL>',
    name: 'Regular User',
    role: 'User',
    status: 'active',
    lastLogin: '2025-07-04T15:45:00Z',
    createdAt: '2024-02-15T00:00:00Z',
    updatedAt: '2025-07-04T15:45:00Z',
    loginAttempts: 0,
    emailVerified: true,
    twoFactorEnabled: false
  },
  {
    id: 3,
    email: '<EMAIL>',
    name: 'Guest User',
    role: 'Guest',
    status: 'inactive',
    lastLogin: null,
    createdAt: '2024-03-01T00:00:00Z',
    updatedAt: '2024-03-01T00:00:00Z',
    loginAttempts: 0,
    emailVerified: false,
    twoFactorEnabled: false
  }
];

let nextUserId = 4;

// Helper functions for file-based storage
async function ensureDataDirectory() {
  const dataDir = path.dirname(DATA_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

async function readUsers() {
  try {
    await ensureDataDirectory();
    const data = await fs.readFile(DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    nextUserId = Math.max(...parsed.users.map((u: any) => u.id)) + 1;
    return parsed.users;
  } catch {
    // File doesn't exist, return default users
    await writeUsers(defaultUsers);
    return defaultUsers;
  }
}

async function writeUsers(users: any[]) {
  await ensureDataDirectory();
  await fs.writeFile(DATA_FILE, JSON.stringify({ users, lastUpdated: new Date().toISOString() }, null, 2));
}

export async function GET(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    // Read users from file storage
    const users = await readUsers();

    return NextResponse.json({
      success: true,
      users: users
    });

  } catch (error) {
    console.error('Auth users error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get authentication users' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { email, name, password, role } = body;

    // Validate required fields
    if (!email || !name || !password || !role) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, message: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate password strength
    if (password.length < 8) {
      return NextResponse.json(
        { success: false, message: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Validate role
    if (!['Admin', 'User', 'Guest'].includes(role)) {
      return NextResponse.json(
        { success: false, message: 'Invalid role' },
        { status: 400 }
      );
    }

    // Read current users
    const users = await readUsers();

    // Check if email already exists
    const existingUser = users.find(user => user.email.toLowerCase() === email.toLowerCase());
    if (existingUser) {
      return NextResponse.json(
        { success: false, message: 'Email already exists' },
        { status: 400 }
      );
    }

    // Create new user
    const newUser = {
      id: nextUserId++,
      email,
      name,
      role,
      status: 'active',
      lastLogin: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      loginAttempts: 0,
      emailVerified: false,
      twoFactorEnabled: false
    };

    // Add to storage
    users.push(newUser);
    await writeUsers(users);

    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      user: newUser
    });

  } catch (error) {
    console.error('Create auth user error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create user' },
      { status: 500 }
    );
  }
}
