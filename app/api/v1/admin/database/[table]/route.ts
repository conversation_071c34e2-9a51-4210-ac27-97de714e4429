import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { table: string } }
) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const table = params.table;

    // Mock table data - in production, this would query the actual database
    const mockData: Record<string, any[]> = {
      users: [
        {
          id: 1,
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'Admin',
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-07-05T08:15:00Z'
        },
        {
          id: 2,
          name: 'Demo User',
          email: '<EMAIL>',
          role: 'Guest',
          createdAt: '2024-02-20T14:45:00Z',
          updatedAt: '2024-07-04T16:20:00Z'
        },
        {
          id: 3,
          name: 'John Smith',
          email: '<EMAIL>',
          role: 'User',
          createdAt: '2024-03-10T09:15:00Z',
          updatedAt: '2024-07-03T11:30:00Z'
        }
      ],
      sessions: [
        {
          id: 1,
          userId: 1,
          token: 'token_admin_session',
          expiresAt: '2024-07-06T10:00:00Z',
          createdAt: '2024-07-05T10:00:00Z'
        },
        {
          id: 2,
          userId: 2,
          token: 'token_demo_session',
          expiresAt: '2024-07-06T12:00:00Z',
          createdAt: '2024-07-05T12:00:00Z'
        }
      ],
      contacts: [
        {
          id: 1,
          name: 'Jane Doe',
          email: '<EMAIL>',
          message: 'Interested in your analytics services',
          status: 'new',
          createdAt: '2024-07-05T09:30:00Z'
        },
        {
          id: 2,
          name: 'Bob Wilson',
          email: '<EMAIL>',
          message: 'Need help with implementation',
          status: 'responded',
          createdAt: '2024-07-04T15:20:00Z'
        }
      ],
      analytics: [
        {
          id: 1,
          event: 'page_view',
          userId: null,
          data: '{"page": "/", "referrer": "google.com"}',
          timestamp: '2024-07-05T10:15:00Z'
        },
        {
          id: 2,
          event: 'login',
          userId: 1,
          data: '{"method": "email"}',
          timestamp: '2024-07-05T10:30:00Z'
        }
      ],
      logs: [
        {
          id: 1,
          level: 'info',
          message: 'User login successful',
          data: '{"userId": 1, "ip": "*************"}',
          timestamp: '2024-07-05T10:30:00Z'
        },
        {
          id: 2,
          level: 'warning',
          message: 'Failed login attempt',
          data: '{"email": "<EMAIL>", "ip": "************"}',
          timestamp: '2024-07-05T10:25:00Z'
        }
      ]
    };

    const tableData = mockData[table] || [];
    const total = tableData.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = tableData.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      data: paginatedData,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Database table data error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get table data' },
      { status: 500 }
    );
  }
}
