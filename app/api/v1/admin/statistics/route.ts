import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    // For demo purposes, return mock admin statistics
    // In production, this would fetch real data from database
    const statistics = {
      users: {
        total: 156,
        byRole: {
          'Admin': 3,
          'User': 142,
          'Guest': 11
        }
      },
      recentUsers: [
        {
          id: 1,
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'Admin',
          createdAt: new Date().toISOString()
        },
        {
          id: 2,
          name: 'Demo User',
          email: '<EMAIL>',
          role: 'Guest',
          createdAt: new Date(Date.now() - 86400000).toISOString()
        },
        {
          id: 3,
          name: '<PERSON>',
          email: '<EMAIL>',
          role: 'User',
          createdAt: new Date(Date.now() - 172800000).toISOString()
        }
      ],
      sessions: {
        active: 23
      },
      analytics: {
        pageViews: 1247,
        uniqueVisitors: 892,
        bounceRate: 34.2,
        avgSessionDuration: 245
      },
      system: {
        uptime: 99.97,
        responseTime: 142,
        errorRate: 0.03
      }
    };

    return NextResponse.json({
      success: true,
      statistics: statistics
    });

  } catch (error) {
    console.error('Admin statistics error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get admin statistics' },
      { status: 500 }
    );
  }
}
