import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get environment variables
    const clientId = process.env.GOOGLE_CLIENT_ID || '65757086638-4v5298gf1nq5bl48sblfiq7mvaf5chg3.apps.googleusercontent.com';

    // Determine the correct base URL based on the request host
    const host = request.headers.get('host');
    const baseUrl = host?.includes('omnilyzer.ai') && !host?.includes('dev.')
      ? 'https://omnilyzer.ai'
      : 'https://dev.omnilyzer.ai';
    const redirectUri = `${baseUrl}/auth/callback`;
    const scope = 'profile email';
    const state = 'oauth_state_' + Date.now();

    // Build OAuth URL
    const oauthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
      `response_type=code&` +
      `client_id=${encodeURIComponent(clientId)}&` +
      `redirect_uri=${encodeURIComponent(redirectUri)}&` +
      `scope=${encodeURIComponent(scope)}&` +
      `state=${encodeURIComponent(state)}`;

    console.log('🎯 Returning OAuth URL as JSON for environment:', process.env.NODE_ENV);
    console.log('🔗 OAuth URL:', oauthUrl);

    return NextResponse.json({
      success: true,
      oauthUrl: oauthUrl,
      state: state,
      environment: process.env.NODE_ENV || 'development'
    });

  } catch (error) {
    console.error('Google OAuth URL API error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to generate OAuth URL' },
      { status: 500 }
    );
  }
}
