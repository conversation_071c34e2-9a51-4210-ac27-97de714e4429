import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Simple token validation (in production, use proper JWT verification)
    if (!token || !token.startsWith('token_')) {
      return NextResponse.json(
        { success: false, message: 'Invalid token format' },
        { status: 401 }
      );
    }

    // For demo purposes, return user based on token pattern
    // In production, decode JWT and fetch user from database
    let user;

    // Simple token-based user identification
    // In a real app, you'd decode the JWT or look up the session in database
    // For now, we'll assume all valid tokens belong to admin user
    user = {
      id: 1,
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'Admin',
      emailVerified: true,
      profileImage: null
    };

    return NextResponse.json({
      success: true,
      user: user
    });

  } catch (error) {
    console.error('Get current user error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get user information' },
      { status: 500 }
    );
  }
}
