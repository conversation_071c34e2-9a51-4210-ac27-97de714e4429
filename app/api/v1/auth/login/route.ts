import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

// File-based storage for user authentication
const DATA_FILE = path.join(process.cwd(), 'data', 'auth-users.json');

async function readUsers() {
  try {
    const data = await fs.readFile(DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.users;
  } catch {
    return [];
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { success: false, message: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Read users from the user management system
    const users = await readUsers();

    // Check for hardcoded admin credentials (fallback)
    if (email === '<EMAIL>' && password === 'admin123') {
      const user = {
        id: 1,
        email: '<EMAIL>',
        name: 'System Administrator',
        role: 'Admin',
        emailVerified: true
      };

      const accessToken = `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      return NextResponse.json({
        success: true,
        message: 'Login successful',
        accessToken,
        user
      });
    }

    // Check users from the user management system
    const foundUser = users.find(user => user.email.toLowerCase() === email.toLowerCase());

    if (foundUser) {
      // Check if user is active
      if (foundUser.status !== 'active') {
        return NextResponse.json(
          { success: false, message: 'Account is not active' },
          { status: 401 }
        );
      }

      // For demo purposes, we'll check against some simple passwords
      // In production, passwords would be hashed and compared properly
      let validPassword = false;

      // Check specific user passwords
      if (email === '<EMAIL>' && password === 'henrik123') {
        validPassword = true;
      } else if (email === '<EMAIL>' && password === 'admin123') {
        validPassword = true;
      } else if (email === '<EMAIL>' && password === 'user123') {
        validPassword = true;
      } else if (email === '<EMAIL>' && password === 'guest123') {
        validPassword = true;
      } else if (password === 'admin123' && foundUser.role === 'Admin') {
        validPassword = true;
      } else if (password === 'user123' && foundUser.role === 'User') {
        validPassword = true;
      } else if (password === 'guest123' && foundUser.role === 'Guest') {
        validPassword = true;
      }

      if (validPassword) {
        const accessToken = `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        return NextResponse.json({
          success: true,
          message: 'Login successful',
          accessToken,
          user: {
            id: foundUser.id,
            email: foundUser.email,
            name: foundUser.name,
            role: foundUser.role,
            emailVerified: foundUser.emailVerified
          }
        });
      }
    }

    // Invalid credentials
    return NextResponse.json(
      { success: false, message: 'Invalid email or password' },
      { status: 401 }
    );

  } catch (error) {
    console.error('Login API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
