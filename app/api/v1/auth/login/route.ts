import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { success: false, message: 'Email and password are required' },
        { status: 400 }
      );
    }

    // For now, implement a simple admin login
    // In production, this should connect to a proper database
    if (email === '<EMAIL>' && password === 'admin123') {
      const user = {
        id: 1,
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'Admin',
        emailVerified: true
      };

      // Generate a simple token (in production, use proper JWT)
      const accessToken = `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      return NextResponse.json({
        success: true,
        message: 'Login successful',
        accessToken,
        user
      });
    }

    // Check for demo credentials
    if (email === '<EMAIL>' && password === 'demo123') {
      const user = {
        id: 2,
        email: '<EMAIL>',
        name: 'Demo User',
        role: 'Guest',
        emailVerified: true
      };

      const accessToken = `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      return NextResponse.json({
        success: true,
        message: 'Login successful',
        accessToken,
        user
      });
    }

    // Invalid credentials
    return NextResponse.json(
      { success: false, message: 'Invalid email or password' },
      { status: 401 }
    );

  } catch (error) {
    console.error('Login API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
