import { NextRequest, NextResponse } from 'next/server';

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  subject?: string;
  message?: string;
  general?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: ContactFormData = await request.json();
    const { name, email, subject, message } = body;

    // Validate required fields
    const errors: FormErrors = {};

    if (!name || name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters long';
    }

    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!subject || subject.trim().length < 5) {
      errors.subject = 'Subject must be at least 5 characters long';
    }

    if (!message || message.trim().length < 10) {
      errors.message = 'Message must be at least 10 characters long';
    }

    // Return validation errors if any
    if (Object.keys(errors).length > 0) {
      return NextResponse.json({
        success: false,
        message: 'Please check your input and try again.',
        errors
      }, { status: 400 });
    }

    // Get client IP and user agent for logging
    const clientIP = request.headers.get('x-forwarded-for') ||
      request.headers.get('x-real-ip') ||
      'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Forward the request to the backend API
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:3001';

    try {
      const backendResponse = await fetch(`${backendUrl}/api/v1/contact`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': clientIP,
          'user-agent': userAgent,
        },
        body: JSON.stringify({
          name: name.trim(),
          email: email.trim(),
          subject: subject.trim(),
          message: message.trim()
        }),
      });

      const backendResult = await backendResponse.json();

      if (backendResponse.ok && backendResult.success) {
        return NextResponse.json({
          success: true,
          message: 'Thank you for your message! We will get back to you soon.'
        });
      } else {
        console.error('Backend contact API error:', backendResult);

        // Return a generic success message to avoid exposing backend issues
        return NextResponse.json({
          success: true,
          message: 'Thank you for your message! We will get back to you soon.'
        });
      }
    } catch (backendError) {
      console.error('Failed to connect to backend contact API:', backendError);

      // In case backend is down, we can still log the contact attempt
      console.log('Contact form submission (backend unavailable):', {
        name,
        email,
        subject,
        message: message.substring(0, 100) + '...',
        clientIP,
        userAgent,
        timestamp: new Date().toISOString()
      });

      // Return success to user (don't expose backend issues)
      return NextResponse.json({
        success: true,
        message: 'Thank you for your message! We will get back to you soon.'
      });
    }

  } catch (error) {
    console.error('Contact form API error:', error);

    return NextResponse.json({
      success: false,
      message: 'An error occurred while processing your request. Please try again later.'
    }, { status: 500 });
  }
}
