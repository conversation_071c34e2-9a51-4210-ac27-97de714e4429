import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '7d';

    // Generate analytics dashboard data based on timeframe
    const now = new Date();
    let startDate: Date;

    switch (timeframe) {
      case '24h':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // Mock analytics data - in production, this would query real analytics database
    const analyticsData = {
      summary: {
        totalPageViews: 12470,
        uniqueVisitors: 8920,
        bounceRate: 34.2,
        avgSessionDuration: 245,
        conversionRate: 2.8,
        topPages: [
          { path: '/', views: 4567, percentage: 36.6 },
          { path: '/services', views: 2341, percentage: 18.8 },
          { path: '/about', views: 1876, percentage: 15.0 },
          { path: '/contact', views: 1234, percentage: 9.9 },
          { path: '/login', views: 987, percentage: 7.9 }
        ],
        topReferrers: [
          { source: 'google.com', visits: 3456, percentage: 38.7 },
          { source: 'direct', visits: 2890, percentage: 32.4 },
          { source: 'linkedin.com', visits: 1234, percentage: 13.8 },
          { source: 'facebook.com', visits: 876, percentage: 9.8 },
          { source: 'twitter.com', visits: 464, percentage: 5.2 }
        ]
      },
      timeSeries: Array.from({ length: timeframe === '24h' ? 24 : timeframe === '7d' ? 7 : 30 }, (_, i) => {
        const date = new Date(startDate.getTime() + i * (timeframe === '24h' ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000));
        return {
          date: date.toISOString(),
          pageViews: Math.floor(Math.random() * 500) + 200,
          uniqueVisitors: Math.floor(Math.random() * 300) + 150,
          sessions: Math.floor(Math.random() * 400) + 180,
          bounceRate: Math.random() * 20 + 25
        };
      }),
      trends: {
        pageViews: Array.from({ length: timeframe === '24h' ? 24 : timeframe === '7d' ? 7 : 30 }, (_, i) => {
          const date = new Date(startDate.getTime() + i * (timeframe === '24h' ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000));
          return {
            date: date.toISOString(),
            views: Math.floor(Math.random() * 500) + 200,
            uniqueViews: Math.floor(Math.random() * 300) + 150
          };
        }),
        userGrowth: Array.from({ length: timeframe === '24h' ? 24 : timeframe === '7d' ? 7 : 30 }, (_, i) => {
          const date = new Date(startDate.getTime() + i * (timeframe === '24h' ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000));
          return {
            date: date.toISOString(),
            count: Math.floor(Math.random() * 50) + 10
          };
        })
      },
      contactsByStatus: [
        { status: 'pending', count: 45 },
        { status: 'responded', count: 123 },
        { status: 'resolved', count: 89 },
        { status: 'archived', count: 34 }
      ],
      userBehavior: {
        deviceTypes: [
          { type: 'Desktop', count: 5234, percentage: 58.7 },
          { type: 'Mobile', count: 2890, percentage: 32.4 },
          { type: 'Tablet', count: 796, percentage: 8.9 }
        ],
        browsers: [
          { name: 'Chrome', count: 4567, percentage: 51.2 },
          { name: 'Safari', count: 2341, percentage: 26.2 },
          { name: 'Firefox', count: 1234, percentage: 13.8 },
          { name: 'Edge', count: 567, percentage: 6.4 },
          { name: 'Other', count: 211, percentage: 2.4 }
        ],
        countries: [
          { country: 'United States', count: 3456, percentage: 38.7 },
          { country: 'Denmark', count: 2890, percentage: 32.4 },
          { country: 'Germany', count: 1234, percentage: 13.8 },
          { country: 'United Kingdom', count: 876, percentage: 9.8 },
          { country: 'Other', count: 464, percentage: 5.2 }
        ]
      },
      goals: {
        contactFormSubmissions: 234,
        newsletterSignups: 567,
        downloadRequests: 123,
        demoRequests: 89
      },
      realTime: {
        activeUsers: 23,
        currentPageViews: 45,
        topActivePages: [
          { path: '/', activeUsers: 12 },
          { path: '/services', activeUsers: 8 },
          { path: '/about', activeUsers: 3 }
        ]
      },
      generatedAt: new Date().toISOString(),
      timeframe: timeframe
    };

    return NextResponse.json({
      success: true,
      data: analyticsData
    });

  } catch (error) {
    console.error('Analytics dashboard error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get analytics dashboard data' },
      { status: 500 }
    );
  }
}
