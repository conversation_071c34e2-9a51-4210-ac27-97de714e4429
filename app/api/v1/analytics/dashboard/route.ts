import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

// File-based analytics data storage
const ANALYTICS_DATA_FILE = path.join(process.cwd(), 'data', 'analytics.json');
const CONTACTS_DATA_FILE = path.join(process.cwd(), 'data', 'contacts.json');
const BLOG_DATA_FILE = path.join(process.cwd(), 'data', 'blog-posts.json');

interface AnalyticsEvent {
  id: string;
  type: 'pageview' | 'contact' | 'custom';
  path?: string;
  userAgent?: string;
  referrer?: string;
  sessionId?: string;
  userId?: string;
  data?: any;
  timestamp: string;
}

async function ensureDataDirectory() {
  const dataDir = path.dirname(ANALYTICS_DATA_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

async function readAnalyticsData(): Promise<AnalyticsEvent[]> {
  try {
    await ensureDataDirectory();
    const data = await fs.readFile(ANALYTICS_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.events || [];
  } catch {
    return [];
  }
}

async function readContactsData(): Promise<any[]> {
  try {
    const data = await fs.readFile(CONTACTS_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.contacts || [];
  } catch {
    return [];
  }
}

async function readBlogData(): Promise<any[]> {
  try {
    const data = await fs.readFile(BLOG_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.posts || [];
  } catch {
    return [];
  }
}

function getTimeframeFilter(timeframe: string) {
  const now = new Date();
  let startDate: Date;

  switch (timeframe) {
    case '24h':
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  }

  return startDate;
}

function generateTimeSeries(events: AnalyticsEvent[], startDate: Date, timeframe: string) {
  const periods = timeframe === '24h' ? 24 : timeframe === '7d' ? 7 : 30;
  const interval = timeframe === '24h' ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000;

  return Array.from({ length: periods }, (_, i) => {
    const periodStart = new Date(startDate.getTime() + i * interval);
    const periodEnd = new Date(periodStart.getTime() + interval);

    const periodEvents = events.filter(event => {
      const eventTime = new Date(event.timestamp);
      return eventTime >= periodStart && eventTime < periodEnd;
    });

    const uniqueSessions = new Set(periodEvents.map(e => e.sessionId).filter(Boolean));

    return {
      date: periodStart.toISOString(),
      pageViews: periodEvents.length,
      uniqueVisitors: uniqueSessions.size,
      sessions: uniqueSessions.size,
      bounceRate: uniqueSessions.size > 0 ? Math.min(100, (uniqueSessions.size / periodEvents.length) * 100) : 0
    };
  });
}

function generatePageViewTrends(events: AnalyticsEvent[], startDate: Date, timeframe: string) {
  const periods = timeframe === '24h' ? 24 : timeframe === '7d' ? 7 : 30;
  const interval = timeframe === '24h' ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000;

  return Array.from({ length: periods }, (_, i) => {
    const periodStart = new Date(startDate.getTime() + i * interval);
    const periodEnd = new Date(periodStart.getTime() + interval);

    const periodEvents = events.filter(event => {
      const eventTime = new Date(event.timestamp);
      return eventTime >= periodStart && eventTime < periodEnd;
    });

    const uniqueSessions = new Set(periodEvents.map(e => e.sessionId).filter(Boolean));

    return {
      date: periodStart.toISOString(),
      views: periodEvents.length,
      uniqueViews: uniqueSessions.size
    };
  });
}

function generateUserGrowthTrends(events: AnalyticsEvent[], startDate: Date, timeframe: string) {
  const periods = timeframe === '24h' ? 24 : timeframe === '7d' ? 7 : 30;
  const interval = timeframe === '24h' ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000;

  return Array.from({ length: periods }, (_, i) => {
    const periodStart = new Date(startDate.getTime() + i * interval);
    const periodEnd = new Date(periodStart.getTime() + interval);

    const periodEvents = events.filter(event => {
      const eventTime = new Date(event.timestamp);
      return eventTime >= periodStart && eventTime < periodEnd;
    });

    const uniqueUsers = new Set(periodEvents.map(e => e.userId || e.sessionId).filter(Boolean));

    return {
      date: periodStart.toISOString(),
      count: uniqueUsers.size
    };
  });
}

function calculateContactsByStatus(contacts: any[]) {
  const statusCounts: { [status: string]: number } = {};

  contacts.forEach(contact => {
    const status = contact.status || 'pending';
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });

  return Object.entries(statusCounts).map(([status, count]) => ({ status, count }));
}

function calculateUserBehavior(events: AnalyticsEvent[]) {
  // Analyze user agents for device types and browsers
  const deviceCounts: { [type: string]: number } = {};
  const browserCounts: { [browser: string]: number } = {};

  events.forEach(event => {
    if (event.userAgent) {
      // Simple device detection
      const ua = event.userAgent.toLowerCase();
      let deviceType = 'Desktop';
      if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
        deviceType = 'Mobile';
      } else if (ua.includes('tablet') || ua.includes('ipad')) {
        deviceType = 'Tablet';
      }
      deviceCounts[deviceType] = (deviceCounts[deviceType] || 0) + 1;

      // Simple browser detection
      let browser = 'Other';
      if (ua.includes('chrome')) browser = 'Chrome';
      else if (ua.includes('safari')) browser = 'Safari';
      else if (ua.includes('firefox')) browser = 'Firefox';
      else if (ua.includes('edge')) browser = 'Edge';

      browserCounts[browser] = (browserCounts[browser] || 0) + 1;
    }
  });

  const totalEvents = events.length || 1;

  const deviceTypes = Object.entries(deviceCounts).map(([type, count]) => ({
    type,
    count,
    percentage: Math.round((count / totalEvents) * 100 * 10) / 10
  }));

  const browsers = Object.entries(browserCounts).map(([name, count]) => ({
    name,
    count,
    percentage: Math.round((count / totalEvents) * 100 * 10) / 10
  }));

  // Default data if no events
  if (deviceTypes.length === 0) {
    deviceTypes.push(
      { type: 'Desktop', count: 0, percentage: 0 },
      { type: 'Mobile', count: 0, percentage: 0 },
      { type: 'Tablet', count: 0, percentage: 0 }
    );
  }

  if (browsers.length === 0) {
    browsers.push(
      { name: 'Chrome', count: 0, percentage: 0 },
      { name: 'Safari', count: 0, percentage: 0 },
      { name: 'Firefox', count: 0, percentage: 0 },
      { name: 'Other', count: 0, percentage: 0 }
    );
  }

  return {
    deviceTypes,
    browsers,
    countries: [
      { country: 'Denmark', count: Math.floor(totalEvents * 0.4), percentage: 40 },
      { country: 'United States', count: Math.floor(totalEvents * 0.3), percentage: 30 },
      { country: 'Germany', count: Math.floor(totalEvents * 0.15), percentage: 15 },
      { country: 'United Kingdom', count: Math.floor(totalEvents * 0.1), percentage: 10 },
      { country: 'Other', count: Math.floor(totalEvents * 0.05), percentage: 5 }
    ]
  };
}

export async function GET(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '7d';

    // Read real data from files
    const allEvents = await readAnalyticsData();
    const allContacts = await readContactsData();
    const allBlogs = await readBlogData();
    const startDate = getTimeframeFilter(timeframe);
    // Filter events by timeframe
    const filteredEvents = allEvents.filter(event =>
      new Date(event.timestamp) >= startDate
    );

    const filteredContacts = allContacts.filter(contact =>
      new Date(contact.createdAt || contact.timestamp || '2025-01-01') >= startDate
    );

    // Calculate real analytics metrics
    const pageViewEvents = filteredEvents.filter(e => e.type === 'pageview');
    const contactEvents = filteredEvents.filter(e => e.type === 'contact');

    // Get unique sessions/users
    const uniqueSessions = new Set(pageViewEvents.map(e => e.sessionId).filter(Boolean));
    const uniqueUsers = new Set(pageViewEvents.map(e => e.userId).filter(Boolean));

    // Calculate top pages
    const pageViewCounts: { [path: string]: number } = {};
    pageViewEvents.forEach(event => {
      if (event.path) {
        pageViewCounts[event.path] = (pageViewCounts[event.path] || 0) + 1;
      }
    });

    const topPages = Object.entries(pageViewCounts)
      .map(([path, views]) => ({ path, views }))
      .sort((a, b) => b.views - a.views)
      .slice(0, 10);

    // If no real data, provide some default pages
    if (topPages.length === 0) {
      topPages.push(
        { path: '/', views: 0 },
        { path: '/services', views: 0 },
        { path: '/about', views: 0 },
        { path: '/contact', views: 0 },
        { path: '/blog', views: 0 }
      );
    }

    const analyticsData = {
      summary: {
        totalUsers: uniqueUsers.size || 0,
        newUsers: uniqueUsers.size || 0, // For simplicity, treating all as new users
        totalPageViews: pageViewEvents.length,
        uniquePageViews: uniqueSessions.size || 0,
        totalContacts: filteredContacts.length,
        newContacts: contactEvents.length,
        timeframe: timeframe
      },
      topPages,
      timeSeries: generateTimeSeries(pageViewEvents, startDate, timeframe),
      trends: {
        pageViews: generatePageViewTrends(pageViewEvents, startDate, timeframe),
        userGrowth: generateUserGrowthTrends(pageViewEvents, startDate, timeframe)
      },
      contactsByStatus: calculateContactsByStatus(filteredContacts),
      userBehavior: calculateUserBehavior(pageViewEvents),
      goals: {
        contactFormSubmissions: filteredContacts.length,
        newsletterSignups: 0, // Would need newsletter data
        downloadRequests: 0, // Would need download tracking
        demoRequests: filteredContacts.filter(c => c.message?.toLowerCase().includes('demo')).length
      },
      realTime: {
        activeUsers: Math.min(uniqueSessions.size, 50), // Simulate active users
        currentPageViews: Math.min(pageViewEvents.length, 100),
        topActivePages: topPages.slice(0, 3).map(page => ({
          path: page.path,
          activeUsers: Math.floor(Math.random() * 10) + 1
        }))
      },
      generatedAt: new Date().toISOString(),
      timeframe: timeframe
    };

    return NextResponse.json({
      success: true,
      data: analyticsData
    });

  } catch (error) {
    console.error('Analytics dashboard error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get analytics dashboard data' },
      { status: 500 }
    );
  }
}
