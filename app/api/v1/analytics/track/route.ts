import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// File-based analytics data storage
const ANALYTICS_DATA_FILE = path.join(process.cwd(), 'data', 'analytics.json');

interface AnalyticsEvent {
  id: string;
  type: 'pageview' | 'contact' | 'custom';
  path?: string;
  userAgent?: string;
  referrer?: string;
  sessionId?: string;
  userId?: string;
  data?: any;
  timestamp: string;
}

async function ensureAnalyticsDataDirectory() {
  const dataDir = path.dirname(ANALYTICS_DATA_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

async function readAnalyticsData(): Promise<AnalyticsEvent[]> {
  try {
    await ensureAnalyticsDataDirectory();
    const data = await fs.readFile(ANALYTICS_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.events || [];
  } catch {
    return [];
  }
}

async function writeAnalyticsData(events: AnalyticsEvent[]) {
  await ensureAnalyticsDataDirectory();
  await fs.writeFile(ANALYTICS_DATA_FILE, JSON.stringify({ 
    events, 
    lastUpdated: new Date().toISOString() 
  }, null, 2));
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, path: eventPath, data } = body;

    if (!type) {
      return NextResponse.json(
        { success: false, message: 'Event type is required' },
        { status: 400 }
      );
    }

    // Extract headers for analytics
    const userAgent = request.headers.get('user-agent') || '';
    const referrer = request.headers.get('referer') || '';
    
    // Generate or extract session ID
    const sessionId = body.sessionId || uuidv4();
    const userId = body.userId || null;

    // Create analytics event
    const event: AnalyticsEvent = {
      id: uuidv4(),
      type,
      path: eventPath,
      userAgent,
      referrer,
      sessionId,
      userId,
      data,
      timestamp: new Date().toISOString()
    };

    // Read existing events
    const events = await readAnalyticsData();
    
    // Add new event
    events.push(event);
    
    // Keep only last 10,000 events to prevent file from growing too large
    if (events.length > 10000) {
      events.splice(0, events.length - 10000);
    }
    
    // Write back to file
    await writeAnalyticsData(events);

    return NextResponse.json({
      success: true,
      message: 'Event tracked successfully',
      eventId: event.id
    });

  } catch (error) {
    console.error('Analytics tracking error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to track event' },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve analytics data (for admin use)
export async function GET(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');

    const events = await readAnalyticsData();
    
    // Filter by type if specified
    let filteredEvents = events;
    if (type) {
      filteredEvents = events.filter(event => event.type === type);
    }

    // Apply pagination
    const paginatedEvents = filteredEvents
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(offset, offset + limit);

    return NextResponse.json({
      success: true,
      data: {
        events: paginatedEvents,
        total: filteredEvents.length,
        limit,
        offset
      }
    });

  } catch (error) {
    console.error('Analytics retrieval error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to retrieve analytics data' },
      { status: 500 }
    );
  }
}
