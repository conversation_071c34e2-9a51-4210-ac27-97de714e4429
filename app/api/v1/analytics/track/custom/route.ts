import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Log analytics data (works in both development and production)
    console.log('Analytics custom:', body);

    // Always return success for now (can implement actual storage later)
    return NextResponse.json({
      success: true,
      message: 'Custom event tracked successfully',
      environment: process.env.NODE_ENV
    });
  } catch (error) {
    console.error('Analytics error:', error);
    return NextResponse.json({ success: false, error: 'Analytics error' }, { status: 500 });
  }
}
