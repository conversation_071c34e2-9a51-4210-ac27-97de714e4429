import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '7d';

    // Generate analytics overview data based on timeRange
    const now = new Date();
    let startDate: Date;
    
    switch (timeRange) {
      case '24h':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // Mock analytics overview data
    const overviewData = {
      summary: {
        totalPageViews: 12470,
        uniqueVisitors: 8920,
        bounceRate: 34.2,
        avgSessionDuration: 245,
        conversionRate: 2.8,
        totalUsers: 1234,
        newUsers: 89,
        totalContacts: 456,
        newContacts: 23
      },
      trends: {
        pageViews: Array.from({ length: timeRange === '24h' ? 24 : timeRange === '7d' ? 7 : 30 }, (_, i) => {
          const date = new Date(startDate.getTime() + i * (timeRange === '24h' ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000));
          return {
            date: date.toISOString(),
            views: Math.floor(Math.random() * 500) + 200,
            uniqueViews: Math.floor(Math.random() * 300) + 150
          };
        }),
        userGrowth: Array.from({ length: timeRange === '24h' ? 24 : timeRange === '7d' ? 7 : 30 }, (_, i) => {
          const date = new Date(startDate.getTime() + i * (timeRange === '24h' ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000));
          return {
            date: date.toISOString(),
            count: Math.floor(Math.random() * 50) + 10
          };
        })
      },
      contactsByStatus: [
        { status: 'pending', count: 45 },
        { status: 'responded', count: 123 },
        { status: 'resolved', count: 89 },
        { status: 'archived', count: 34 }
      ],
      topPages: [
        { path: '/', views: 4567, percentage: 36.6 },
        { path: '/services', views: 2341, percentage: 18.8 },
        { path: '/about', views: 1876, percentage: 15.0 },
        { path: '/contact', views: 1234, percentage: 9.9 },
        { path: '/login', views: 987, percentage: 7.9 }
      ],
      topReferrers: [
        { source: 'google.com', visits: 3456, percentage: 38.7 },
        { source: 'direct', visits: 2890, percentage: 32.4 },
        { source: 'linkedin.com', visits: 1234, percentage: 13.8 },
        { source: 'facebook.com', visits: 876, percentage: 9.8 },
        { source: 'twitter.com', visits: 464, percentage: 5.2 }
      ],
      generatedAt: new Date().toISOString(),
      timeRange: timeRange
    };

    return NextResponse.json({
      success: true,
      data: overviewData
    });

  } catch (error) {
    console.error('Analytics overview error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get analytics overview data' },
      { status: 500 }
    );
  }
}
