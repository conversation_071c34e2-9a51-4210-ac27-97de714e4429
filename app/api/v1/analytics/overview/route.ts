import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

// File-based analytics data storage
const ANALYTICS_DATA_FILE = path.join(process.cwd(), 'data', 'analytics.json');
const CONTACTS_DATA_FILE = path.join(process.cwd(), 'data', 'contacts.json');
const BLOG_DATA_FILE = path.join(process.cwd(), 'data', 'blog-posts.json');
const SOCIAL_DATA_FILE = path.join(process.cwd(), 'data', 'social-posts.json');

interface AnalyticsEvent {
  id: string;
  type: 'pageview' | 'contact' | 'custom';
  path?: string;
  userAgent?: string;
  referrer?: string;
  sessionId?: string;
  userId?: string;
  data?: any;
  timestamp: string;
}

async function readAnalyticsData(): Promise<AnalyticsEvent[]> {
  try {
    const data = await fs.readFile(ANALYTICS_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.events || [];
  } catch {
    return [];
  }
}

async function readContactsData(): Promise<any[]> {
  try {
    const data = await fs.readFile(CONTACTS_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.contacts || [];
  } catch {
    return [];
  }
}

async function readBlogData(): Promise<any[]> {
  try {
    const data = await fs.readFile(BLOG_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.posts || [];
  } catch {
    return [];
  }
}

async function readSocialData(): Promise<any[]> {
  try {
    const data = await fs.readFile(SOCIAL_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.posts || [];
  } catch {
    return [];
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '7d';

    // Calculate time range
    const now = new Date();
    let startDate: Date;

    switch (timeRange) {
      case '24h':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // Read real data
    const allEvents = await readAnalyticsData();
    const allContacts = await readContactsData();
    const allBlogs = await readBlogData();
    const allSocialPosts = await readSocialData();

    // Filter by time range
    const filteredEvents = allEvents.filter(event =>
      new Date(event.timestamp) >= startDate
    );

    const pageViewEvents = filteredEvents.filter(e => e.type === 'pageview');
    const uniqueSessions = new Set(pageViewEvents.map(e => e.sessionId).filter(Boolean));

    // Calculate session duration (simplified)
    const avgSessionDuration = pageViewEvents.length > 0 ?
      Math.floor(Math.random() * 300) + 120 : 0; // Placeholder calculation

    // Calculate bounce rate (simplified)
    const bounceRate = uniqueSessions.size > 0 ?
      Math.min(100, (uniqueSessions.size / pageViewEvents.length) * 100) : 0;

    // Real analytics overview data
    const overviewData = {
      totalPageViews: pageViewEvents.length,
      uniqueVisitors: uniqueSessions.size,
      avgSessionDuration: avgSessionDuration,
      bounceRate: Math.round(bounceRate * 10) / 10,
      conversionRate: pageViewEvents.length > 0 ?
        Math.round((allContacts.length / pageViewEvents.length) * 100 * 10) / 10 : 0,
      activeABTests: 0, // Would need A/B test data
      activeFunnels: 0, // Would need funnel data
      emailCampaigns: allSocialPosts.length, // Using social posts as email campaigns
      recentActivity: generateRecentActivity(filteredEvents, allContacts, allBlogs),
      trafficSources: generateTrafficSources(pageViewEvents),
      topPages: generateTopPages(pageViewEvents),
      generatedAt: new Date().toISOString(),
      timeRange: timeRange
    };

    return NextResponse.json({
      success: true,
      data: overviewData
    });

  } catch (error) {
    console.error('Analytics overview error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get analytics overview data' },
      { status: 500 }
    );
  }
}

function generateRecentActivity(events: AnalyticsEvent[], contacts: any[], blogs: any[]) {
  const recentActivity = [];

  // Add recent events
  const recentEvents = events
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, 5);

  recentEvents.forEach(event => {
    if (event.type === 'pageview') {
      recentActivity.push({
        type: 'pageview',
        description: `New page view on ${event.path || 'unknown page'}`,
        timestamp: event.timestamp
      });
    } else if (event.type === 'contact') {
      recentActivity.push({
        type: 'contact',
        description: 'New contact form submission',
        timestamp: event.timestamp
      });
    }
  });

  // Add recent blog posts
  const recentBlogs = blogs
    .filter(blog => blog.status === 'published')
    .sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime())
    .slice(0, 2);

  recentBlogs.forEach(blog => {
    recentActivity.push({
      type: 'content',
      description: `New blog post published: ${blog.title}`,
      timestamp: blog.publishedAt || blog.createdAt
    });
  });

  // Sort by timestamp and return top 10
  return recentActivity
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, 10);
}

function generateTrafficSources(events: AnalyticsEvent[]) {
  const sourceCounts: { [source: string]: number } = {};

  events.forEach(event => {
    let source = 'Direct';
    if (event.referrer) {
      const referrer = event.referrer.toLowerCase();
      if (referrer.includes('google')) source = 'Google';
      else if (referrer.includes('linkedin')) source = 'LinkedIn';
      else if (referrer.includes('facebook')) source = 'Facebook';
      else if (referrer.includes('twitter')) source = 'Twitter';
      else source = 'Referral';
    }
    sourceCounts[source] = (sourceCounts[source] || 0) + 1;
  });

  const total = events.length || 1;
  const sources = Object.entries(sourceCounts)
    .map(([source, visitors]) => ({
      source,
      visitors,
      percentage: Math.round((visitors / total) * 100 * 10) / 10
    }))
    .sort((a, b) => b.visitors - a.visitors);

  // Ensure we have some default sources if no data
  if (sources.length === 0) {
    return [
      { source: 'Direct', visitors: 0, percentage: 0 },
      { source: 'Google', visitors: 0, percentage: 0 },
      { source: 'LinkedIn', visitors: 0, percentage: 0 },
      { source: 'Facebook', visitors: 0, percentage: 0 }
    ];
  }

  return sources.slice(0, 5);
}

function generateTopPages(events: AnalyticsEvent[]) {
  const pageCounts: { [path: string]: number } = {};

  events.forEach(event => {
    if (event.path) {
      pageCounts[event.path] = (pageCounts[event.path] || 0) + 1;
    }
  });

  const total = events.length || 1;
  const pages = Object.entries(pageCounts)
    .map(([path, views]) => ({
      path,
      views,
      percentage: Math.round((views / total) * 100 * 10) / 10
    }))
    .sort((a, b) => b.views - a.views);

  // Ensure we have some default pages if no data
  if (pages.length === 0) {
    return [
      { path: '/', views: 0, percentage: 0 },
      { path: '/services', views: 0, percentage: 0 },
      { path: '/about', views: 0, percentage: 0 },
      { path: '/contact', views: 0, percentage: 0 },
      { path: '/blog', views: 0, percentage: 0 }
    ];
  }

  return pages.slice(0, 10);
}
