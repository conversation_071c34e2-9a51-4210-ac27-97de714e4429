import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '7d';

    // Generate analytics overview data based on timeRange
    const now = new Date();
    let startDate: Date;

    switch (timeRange) {
      case '24h':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // Mock analytics overview data - structured for AnalyticsOverview component
    const overviewData = {
      totalPageViews: 12470,
      uniqueVisitors: 8920,
      avgSessionDuration: 245,
      bounceRate: 34.2,
      conversionRate: 2.8,
      activeABTests: 3,
      activeFunnels: 5,
      emailCampaigns: 8,
      recentActivity: [
        {
          type: 'pageview',
          description: 'New page view on /services',
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString()
        },
        {
          type: 'contact',
          description: 'New contact form submission',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString()
        },
        {
          type: 'conversion',
          description: 'User completed signup funnel',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString()
        }
      ],
      trafficSources: [
        { source: 'Google', visitors: 3456, percentage: 38.7 },
        { source: 'Direct', visitors: 2890, percentage: 32.4 },
        { source: 'LinkedIn', visitors: 1234, percentage: 13.8 },
        { source: 'Facebook', visitors: 876, percentage: 9.8 },
        { source: 'Twitter', visitors: 464, percentage: 5.2 }
      ],
      topPages: [
        { path: '/', views: 4567, percentage: 36.6 },
        { path: '/services', views: 2341, percentage: 18.8 },
        { path: '/about', views: 1876, percentage: 15.0 },
        { path: '/contact', views: 1234, percentage: 9.9 },
        { path: '/login', views: 987, percentage: 7.9 }
      ],
      generatedAt: new Date().toISOString(),
      timeRange: timeRange
    };

    return NextResponse.json({
      success: true,
      data: overviewData
    });

  } catch (error) {
    console.error('Analytics overview error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get analytics overview data' },
      { status: 500 }
    );
  }
}
