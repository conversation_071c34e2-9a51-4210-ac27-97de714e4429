import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '7d';

    // Generate mock heat map summary data
    const heatMapSummary = {
      timeframe: timeframe,
      pageInteractions: [
        { pageUrl: '/', interactions: 1247 },
        { pageUrl: '/services', interactions: 892 },
        { pageUrl: '/about', interactions: 634 },
        { pageUrl: '/contact', interactions: 456 },
        { pageUrl: '/login', interactions: 234 },
        { pageUrl: '/admin', interactions: 123 },
        { pageUrl: '/pricing', interactions: 89 },
        { pageUrl: '/blog', interactions: 67 }
      ],
      elementTypeBreakdown: [
        { elementType: 'click', count: 2847 },
        { elementType: 'hover', count: 1923 },
        { elementType: 'scroll', count: 3456 },
        { elementType: 'form_focus', count: 567 }
      ],
      dailyTrends: Array.from({ length: parseInt(timeframe.replace('d', '')) }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - i);
        return [
          {
            date: date.toISOString().split('T')[0],
            elementType: 'click',
            interactions: Math.floor(Math.random() * 200) + 100
          },
          {
            date: date.toISOString().split('T')[0],
            elementType: 'hover',
            interactions: Math.floor(Math.random() * 150) + 50
          },
          {
            date: date.toISOString().split('T')[0],
            elementType: 'scroll',
            interactions: Math.floor(Math.random() * 300) + 200
          },
          {
            date: date.toISOString().split('T')[0],
            elementType: 'form_focus',
            interactions: Math.floor(Math.random() * 50) + 10
          }
        ];
      }).flat(),
      topElements: [
        {
          elementId: 'nav-home',
          elementClass: 'nav-link',
          elementTag: 'a',
          clicks: 456
        },
        {
          elementId: 'contact-form',
          elementClass: 'form-submit',
          elementTag: 'button',
          clicks: 234
        },
        {
          elementId: 'hero-cta',
          elementClass: 'btn-primary',
          elementTag: 'button',
          clicks: 189
        },
        {
          elementId: 'footer-link',
          elementClass: 'footer-nav',
          elementTag: 'a',
          clicks: 123
        },
        {
          elementId: 'search-input',
          elementClass: 'search-field',
          elementTag: 'input',
          clicks: 89
        }
      ]
    };

    return NextResponse.json({
      success: true,
      data: heatMapSummary
    });

  } catch (error) {
    console.error('Heat map summary error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get heat map summary' },
      { status: 500 }
    );
  }
}
