import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { pageUrl: string } }
) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '7d';
    const pageUrl = decodeURIComponent(params.pageUrl);

    // Generate mock page heat map data
    const pageHeatMap = {
      pageUrl: pageUrl,
      timeframe: timeframe,
      clickHeatMap: Array.from({ length: 50 }, (_, i) => ({
        x: Math.floor(Math.random() * 1200) + 100,
        y: Math.floor(Math.random() * 800) + 100,
        count: Math.floor(Math.random() * 20) + 1,
        intensity: Math.random(),
        elements: [`element-${i}`, `class-${i % 5}`]
      })),
      hoverHeatMap: Array.from({ length: 30 }, (_, i) => ({
        x: Math.floor(Math.random() * 1200) + 100,
        y: Math.floor(Math.random() * 800) + 100,
        count: Math.floor(Math.random() * 15) + 1,
        intensity: Math.random()
      })),
      scrollAnalytics: {
        avgScrollDepth: 67.5,
        maxScrollDepth: 95.2,
        histogram: [
          { depth: 0, count: 1000, percentage: 100 },
          { depth: 25, count: 850, percentage: 85 },
          { depth: 50, count: 675, percentage: 67.5 },
          { depth: 75, count: 450, percentage: 45 },
          { depth: 100, count: 120, percentage: 12 }
        ],
        totalScrollEvents: 3456,
        uniqueSessions: 892
      },
      elementInteractions: [
        {
          elementId: 'header-nav',
          elementClass: 'navigation',
          elementTag: 'nav',
          elementType: 'click',
          count: 234
        },
        {
          elementId: 'main-cta',
          elementClass: 'btn-primary',
          elementTag: 'button',
          elementType: 'click',
          count: 189
        },
        {
          elementId: 'contact-form',
          elementClass: 'form-input',
          elementTag: 'input',
          elementType: 'form_focus',
          count: 156
        },
        {
          elementId: 'footer-links',
          elementClass: 'footer-nav',
          elementTag: 'a',
          elementType: 'hover',
          count: 123
        },
        {
          elementId: 'sidebar-menu',
          elementClass: 'menu-item',
          elementTag: 'li',
          elementType: 'click',
          count: 89
        }
      ],
      summary: {
        totalClicks: 1247,
        totalHovers: 892,
        uniqueSessions: 456
      }
    };

    return NextResponse.json({
      success: true,
      data: pageHeatMap
    });

  } catch (error) {
    console.error('Page heat map error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get page heat map' },
      { status: 500 }
    );
  }
}
