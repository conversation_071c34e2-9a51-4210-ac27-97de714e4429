import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

// File-based storage for social media posts
const SOCIAL_DATA_FILE = path.join(process.cwd(), 'data', 'social-posts.json');

interface SocialPost {
  id: number;
  blogPostId: number;
  platform: 'facebook' | 'linkedin';
  content: string;
  status: 'pending' | 'posted' | 'failed';
  scheduledAt?: string;
  postedAt?: string;
  postId?: string;
  error?: string;
  createdAt: string;
  updatedAt: string;
}

async function ensureSocialDataDirectory() {
  const dataDir = path.dirname(SOCIAL_DATA_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

async function readSocialPosts(): Promise<SocialPost[]> {
  try {
    await ensureSocialDataDirectory();
    const data = await fs.readFile(SOCIAL_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.posts || [];
  } catch {
    return [];
  }
}

async function writeSocialPosts(posts: SocialPost[]) {
  await ensureSocialDataDirectory();
  await fs.writeFile(SOCIAL_DATA_FILE, JSON.stringify({ 
    posts, 
    lastUpdated: new Date().toISOString() 
  }, null, 2));
}

// Mock social media posting functions
async function postToFacebook(content: string, blogUrl: string) {
  // In production, this would use Facebook Graph API
  // For now, we'll simulate the posting process
  
  const facebookPost = {
    message: content,
    link: blogUrl,
    // In real implementation:
    // access_token: process.env.FACEBOOK_ACCESS_TOKEN,
    // page_id: process.env.FACEBOOK_PAGE_ID
  };

  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Simulate success/failure (90% success rate)
  if (Math.random() > 0.1) {
    return {
      success: true,
      postId: `fb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      url: `https://www.facebook.com/profile.php?id=61577959296494/posts/${Date.now()}`
    };
  } else {
    throw new Error('Facebook API error: Rate limit exceeded');
  }
}

async function postToLinkedIn(content: string, blogUrl: string) {
  // In production, this would use LinkedIn API
  // For now, we'll simulate the posting process
  
  const linkedInPost = {
    content: {
      contentEntities: [{
        entityLocation: blogUrl,
        thumbnails: []
      }],
      title: content.split('\n')[0] || 'New Blog Post from Omnilyzer'
    },
    distribution: {
      feedDistribution: 'MAIN_FEED',
      targetEntities: [],
      thirdPartyDistributionChannels: []
    },
    text: {
      text: content
    }
    // In real implementation:
    // headers: {
    //   'Authorization': `Bearer ${process.env.LINKEDIN_ACCESS_TOKEN}`,
    //   'Content-Type': 'application/json'
    // }
  };

  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1500));

  // Simulate success/failure (85% success rate)
  if (Math.random() > 0.15) {
    return {
      success: true,
      postId: `li_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      url: `https://www.linkedin.com/company/omnilyzer/posts/${Date.now()}`
    };
  } else {
    throw new Error('LinkedIn API error: Invalid access token');
  }
}

function generateSocialContent(blogPost: any, platform: 'facebook' | 'linkedin') {
  const baseUrl = process.env.NODE_ENV === 'production' ? 'https://omnilyzer.ai' : 'https://dev.omnilyzer.ai';
  const blogUrl = `${baseUrl}/blog/${blogPost.slug}`;

  if (platform === 'facebook') {
    return `🚀 New Blog Post: ${blogPost.title}

${blogPost.summary}

💡 Key insights for small business owners:
• Practical AI solutions that work
• Real-world implementation examples  
• Concrete steps to get started

Read the full article and discover how Omnilyzer can transform your business operations.

#AI #SmallBusiness #Automation #BusinessIntelligence #Omnilyzer

${blogUrl}`;
  } else { // LinkedIn
    return `🎯 New Insight: ${blogPost.title}

${blogPost.summary}

In this article, we explore:
✅ Practical applications for SMBs
✅ Implementation strategies that work
✅ Real ROI examples from our clients

At Omnilyzer, we believe AI should be accessible to every business, not just tech giants. This post shows exactly how small and medium businesses can leverage AI to:

• Reduce operational costs by up to 40%
• Automate repetitive tasks
• Make data-driven decisions
• Scale operations efficiently

What's your biggest challenge with business automation? Share your thoughts in the comments.

#ArtificialIntelligence #BusinessAutomation #SMB #DigitalTransformation #Omnilyzer

Read more: ${blogUrl}`;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { blogPostId, platforms, scheduleAt } = body;

    if (!blogPostId || !platforms || !Array.isArray(platforms)) {
      return NextResponse.json(
        { success: false, message: 'Blog post ID and platforms are required' },
        { status: 400 }
      );
    }

    // Get blog post data
    const blogResponse = await fetch(`${request.nextUrl.origin}/api/v1/blog?status=all`);
    const blogData = await blogResponse.json();
    const blogPost = blogData.data.posts.find((p: any) => p.id === blogPostId);

    if (!blogPost) {
      return NextResponse.json(
        { success: false, message: 'Blog post not found' },
        { status: 404 }
      );
    }

    const socialPosts = await readSocialPosts();
    const results = [];

    for (const platform of platforms) {
      if (!['facebook', 'linkedin'].includes(platform)) {
        continue;
      }

      const content = generateSocialContent(blogPost, platform);
      const baseUrl = process.env.NODE_ENV === 'production' ? 'https://omnilyzer.ai' : 'https://dev.omnilyzer.ai';
      const blogUrl = `${baseUrl}/blog/${blogPost.slug}`;

      const nextId = Math.max(...socialPosts.map(p => p.id), 0) + 1;

      const socialPost: SocialPost = {
        id: nextId,
        blogPostId,
        platform: platform as 'facebook' | 'linkedin',
        content,
        status: 'pending',
        scheduledAt: scheduleAt || new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      try {
        // If not scheduled for later, post immediately
        if (!scheduleAt || new Date(scheduleAt) <= new Date()) {
          let result;
          if (platform === 'facebook') {
            result = await postToFacebook(content, blogUrl);
          } else {
            result = await postToLinkedIn(content, blogUrl);
          }

          socialPost.status = 'posted';
          socialPost.postedAt = new Date().toISOString();
          socialPost.postId = result.postId;
        }

        socialPosts.push(socialPost);
        results.push({
          platform,
          status: socialPost.status,
          postId: socialPost.postId,
          message: socialPost.status === 'posted' ? 'Posted successfully' : 'Scheduled for later'
        });

      } catch (error) {
        socialPost.status = 'failed';
        socialPost.error = error instanceof Error ? error.message : 'Unknown error';
        socialPosts.push(socialPost);
        
        results.push({
          platform,
          status: 'failed',
          error: socialPost.error
        });
      }
    }

    await writeSocialPosts(socialPosts);

    return NextResponse.json({
      success: true,
      message: 'Social media publishing initiated',
      results
    });

  } catch (error) {
    console.error('Social media publishing error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to publish to social media' },
      { status: 500 }
    );
  }
}
