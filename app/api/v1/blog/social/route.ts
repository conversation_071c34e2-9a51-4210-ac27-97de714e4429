import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

const SOCIAL_DATA_FILE = path.join(process.cwd(), 'data', 'social-posts.json');

interface SocialPost {
  id: number;
  blogPostId: number;
  platform: 'facebook' | 'linkedin';
  content: string;
  status: 'pending' | 'posted' | 'failed';
  scheduledAt?: string;
  postedAt?: string;
  postId?: string;
  error?: string;
  createdAt: string;
  updatedAt: string;
}

async function readSocialPosts(): Promise<SocialPost[]> {
  try {
    const data = await fs.readFile(SOCIAL_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.posts || [];
  } catch {
    return [];
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const blogPostId = searchParams.get('blogPostId');

    let socialPosts = await readSocialPosts();

    // Filter by blog post ID if provided
    if (blogPostId) {
      socialPosts = socialPosts.filter(post => post.blogPostId === parseInt(blogPostId));
    }

    // Sort by created date (newest first)
    socialPosts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return NextResponse.json({
      success: true,
      data: socialPosts
    });

  } catch (error) {
    console.error('Get social posts error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get social posts' },
      { status: 500 }
    );
  }
}
