import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

// File-based storage for blog posts
const BLOG_DATA_FILE = path.join(process.cwd(), 'data', 'blog-posts.json');

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  summary: string;
  content: string;
  author: string;
  status: 'draft' | 'published' | 'archived';
  category?: string;
  tags?: string[];
  readTime?: number;
  trendTopic?: string;
  aiPrompt?: string;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

async function ensureBlogDataDirectory() {
  const dataDir = path.dirname(BLOG_DATA_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

async function readBlogPosts(): Promise<BlogPost[]> {
  try {
    await ensureBlogDataDirectory();
    const data = await fs.readFile(BLOG_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.posts || [];
  } catch {
    return [];
  }
}

async function writeBlogPosts(posts: BlogPost[]) {
  await ensureBlogDataDirectory();
  await fs.writeFile(BLOG_DATA_FILE, JSON.stringify({
    posts,
    lastUpdated: new Date().toISOString()
  }, null, 2));
}

export async function POST(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { trendTopic, customPrompt } = body;

    // Simulate fetching Google Trends data
    const mockTrendData = {
      topic: trendTopic || 'AI automation for small businesses',
      searchVolume: Math.floor(Math.random() * 100000) + 10000,
      relatedQueries: [
        'small business automation tools',
        'AI for SMB',
        'business process automation',
        'automated customer service'
      ],
      region: 'Global',
      timeframe: 'Last 7 days'
    };

    // Generate AI content (mock implementation)
    const aiGeneratedContent = await generateBlogContent(mockTrendData.topic, customPrompt);

    // Create the blog post directly
    const posts = await readBlogPosts();
    const nextId = Math.max(...posts.map(p => p.id), 0) + 1;

    // Generate slug from title
    const slug = aiGeneratedContent.title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    // Calculate read time (average 200 words per minute)
    const wordCount = aiGeneratedContent.content.split(/\s+/).length;
    const readTime = Math.ceil(wordCount / 200);

    const newPost: BlogPost = {
      id: nextId,
      title: aiGeneratedContent.title,
      slug,
      summary: aiGeneratedContent.summary,
      content: aiGeneratedContent.content,
      author: 'Michael Hansen',
      status: 'draft',
      category: aiGeneratedContent.category,
      tags: Array.isArray(aiGeneratedContent.tags) ? aiGeneratedContent.tags : [],
      readTime,
      trendTopic: mockTrendData.topic,
      aiPrompt: aiGeneratedContent.prompt,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    posts.push(newPost);
    await writeBlogPosts(posts);

    return NextResponse.json({
      success: true,
      message: 'Blog post generated successfully',
      data: {
        blogPost: newPost,
        trendData: mockTrendData,
        generationDetails: {
          prompt: aiGeneratedContent.prompt,
          generatedAt: new Date().toISOString(),
          wordCount: aiGeneratedContent.content.split(/\s+/).length
        }
      }
    });

  } catch (error) {
    console.error('Blog generation error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to generate blog post' },
      { status: 500 }
    );
  }
}

async function generateBlogContent(trendTopic: string, customPrompt?: string) {
  // Mock AI content generation
  // In production, this would call OpenAI API

  const prompt = customPrompt || `Write a blog post about "${trendTopic}" for small and medium business owners. 
  
  Structure:
  1. Start with an executive summary (2-3 sentences)
  2. Explain the topic in simple, jargon-free language
  3. Show how it benefits SMBs
  4. End with 3 concrete example solutions that Omnilyzer could build
  
  Tone: Positive, motivational, and accessible
  Target audience: SMB owners who may not be tech-savvy
  Author: Michael Hansen from Omnilyzer`;

  // Simulate different AI-generated content based on topic
  const contentTemplates = {
    'AI automation': {
      title: 'How AI Automation Can Transform Your Small Business Operations',
      category: 'AI Solutions',
      tags: ['AI', 'Automation', 'SMB', 'Efficiency'],
      summary: 'Small businesses are discovering that AI automation isn\'t just for tech giants anymore. Modern AI tools can streamline operations, reduce costs, and free up valuable time for business owners to focus on growth and customer relationships.',
      content: `# How AI Automation Can Transform Your Small Business Operations

## Executive Summary
Small businesses are discovering that AI automation isn't just for tech giants anymore. Modern AI tools can streamline operations, reduce costs by up to 40%, and free up valuable time for business owners to focus on growth and customer relationships.

## The Reality for Small Business Owners
Running a small business means wearing many hats - you're the CEO, accountant, customer service rep, and marketing manager all rolled into one. This constant juggling act leaves little time for strategic thinking and business growth.

## How AI Automation Changes the Game
AI automation can handle repetitive tasks that consume hours of your day:
- **Email Management**: Automatically sort, prioritize, and even respond to routine emails
- **Appointment Scheduling**: Let AI handle booking, rescheduling, and reminders
- **Inventory Tracking**: Automatically reorder supplies when stock runs low
- **Customer Follow-ups**: Send personalized messages based on customer behavior

## The SMB Advantage
Unlike large corporations, small businesses can implement AI solutions quickly and see immediate results. You don't need months of planning or massive IT departments - modern AI tools are designed for simplicity and rapid deployment.

## Three AI Solutions Omnilyzer Can Build for Your Business

### 1. Smart Customer Communication Hub
An AI system that manages all customer communications across email, chat, and social media. It automatically categorizes inquiries, provides instant responses to common questions, and escalates complex issues to you with full context.

### 2. Automated Bookkeeping Assistant
An intelligent system that categorizes expenses, tracks invoices, and generates financial reports. It learns your business patterns and can even predict cash flow trends to help with planning.

### 3. Marketing Campaign Optimizer
An AI tool that analyzes your customer data to create targeted marketing campaigns, optimizes ad spending across platforms, and provides detailed ROI reports so you know exactly what's working.

Ready to reclaim your time and grow your business? Contact Omnilyzer today to discuss how we can build custom AI automation solutions tailored to your specific needs.`
    },
    'default': {
      title: `The Future of ${trendTopic} for Small Business Success`,
      category: 'Business Innovation',
      tags: ['Innovation', 'SMB', 'Technology', 'Growth'],
      summary: `Discover how ${trendTopic} is revolutionizing the way small and medium businesses operate, compete, and grow in today's digital marketplace.`,
      content: `# The Future of ${trendTopic} for Small Business Success

## Executive Summary
${trendTopic} is revolutionizing the way small and medium businesses operate, compete, and grow in today's digital marketplace. Forward-thinking SMBs are leveraging this trend to gain competitive advantages previously available only to large corporations.

## Understanding the Opportunity
The business landscape is changing rapidly, and ${trendTopic} represents a significant opportunity for small businesses to level the playing field. While large companies struggle with bureaucracy and slow decision-making, agile SMBs can quickly adopt new technologies and strategies.

## Why This Matters for Your Business
${trendTopic} offers several key benefits for small business owners:
- **Competitive Advantage**: Stay ahead of competitors who are slow to adapt
- **Cost Efficiency**: Reduce operational expenses while improving quality
- **Customer Satisfaction**: Deliver better experiences that build loyalty
- **Scalability**: Grow your business without proportionally increasing costs

## Getting Started
You don't need to be a technology expert to benefit from ${trendTopic}. The key is to start small, focus on your biggest pain points, and gradually expand as you see results.

## Three Custom Solutions Omnilyzer Can Build

### 1. ${trendTopic} Integration Platform
A comprehensive system that seamlessly integrates ${trendTopic} into your existing business processes, providing immediate benefits without disrupting your current operations.

### 2. Smart Analytics Dashboard
A user-friendly dashboard that tracks the impact of ${trendTopic} on your business, showing clear ROI metrics and identifying opportunities for further optimization.

### 3. Automated ${trendTopic} Management System
An intelligent system that handles the complex aspects of ${trendTopic} automatically, allowing you to focus on running your business while technology works behind the scenes.

Transform your business with ${trendTopic}. Contact Omnilyzer to learn how we can build a custom solution that fits your specific needs and budget.`
    }
  };

  // Select appropriate template based on topic
  let template = contentTemplates.default;
  if (trendTopic.toLowerCase().includes('ai') || trendTopic.toLowerCase().includes('automation')) {
    template = contentTemplates['AI automation'];
  }

  return {
    ...template,
    prompt
  };
}
