import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

// File-based storage for blog posts (demo implementation)
const BLOG_DATA_FILE = path.join(process.cwd(), 'data', 'blog-posts.json');

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  summary: string;
  content: string;
  author: string;
  status: 'draft' | 'published' | 'archived';
  category?: string;
  tags?: string[];
  readTime?: number;
  trendTopic?: string;
  aiPrompt?: string;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

async function ensureBlogDataDirectory() {
  const dataDir = path.dirname(BLOG_DATA_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

async function readBlogPosts(): Promise<BlogPost[]> {
  try {
    await ensureBlogDataDirectory();
    const data = await fs.readFile(BLOG_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.posts || [];
  } catch {
    // File doesn't exist, return default posts
    const defaultPosts: BlogPost[] = [
      {
        id: 1,
        title: 'AI-Powered Customer Service: The Future is Here',
        slug: 'ai-powered-customer-service-future',
        summary: 'Discover how AI chatbots and automation are revolutionizing customer service for small and medium businesses, reducing costs while improving customer satisfaction.',
        content: `# AI-Powered Customer Service: The Future is Here

## Executive Summary
Small and medium businesses are increasingly turning to AI-powered customer service solutions to reduce operational costs while dramatically improving customer satisfaction. Modern AI chatbots can handle 80% of routine inquiries, allowing human agents to focus on complex issues that truly require personal attention.

## The SMB Challenge
Traditional customer service models are expensive and difficult to scale. Hiring, training, and maintaining a 24/7 support team can cost SMBs thousands of dollars monthly, often without guaranteeing consistent service quality.

## How AI Changes Everything
AI-powered customer service solutions offer:
- **24/7 Availability**: Never miss a customer inquiry
- **Instant Responses**: Reduce wait times from hours to seconds
- **Consistent Quality**: Every interaction follows best practices
- **Cost Efficiency**: Reduce support costs by up to 70%

## Three Concrete Solutions Omnilyzer Can Build

### 1. Intelligent FAQ Chatbot
A smart chatbot that learns from your existing support tickets and documentation to answer common questions instantly. Perfect for e-commerce stores, service providers, and SaaS companies.

### 2. Appointment Scheduling Assistant
An AI assistant that handles appointment bookings, cancellations, and rescheduling across multiple calendars. Ideal for healthcare practices, consulting firms, and service businesses.

### 3. Order Status and Tracking System
An automated system that provides real-time order updates, shipping information, and delivery notifications. Essential for retail businesses and online stores.

Ready to transform your customer service? Contact Omnilyzer today to discuss how we can build a custom AI solution for your business.`,
        author: 'Michael Hansen',
        status: 'published',
        category: 'AI Solutions',
        tags: ['AI', 'Customer Service', 'Automation', 'SMB'],
        readTime: 5,
        trendTopic: 'AI customer service automation',
        publishedAt: '2025-07-05T10:00:00Z',
        createdAt: '2025-07-05T09:30:00Z',
        updatedAt: '2025-07-05T09:45:00Z'
      },
      {
        id: 2,
        title: 'Data Analytics for Small Business: Making Sense of Your Numbers',
        slug: 'data-analytics-small-business-guide',
        summary: 'Learn how small businesses can leverage data analytics to make better decisions, increase profits, and compete with larger companies using affordable, easy-to-implement solutions.',
        content: `# Data Analytics for Small Business: Making Sense of Your Numbers

## Executive Summary
Small businesses generate more data than ever before, but most struggle to turn this information into actionable insights. Modern analytics tools can help SMBs make data-driven decisions that increase profits by 15-25% within the first year of implementation.

## Why Data Matters for SMBs
Every customer interaction, sale, and website visit generates valuable data. However, without proper analysis, this information remains just numbers in spreadsheets. Smart analytics can reveal:
- Which products or services are most profitable
- When customers are most likely to buy
- Which marketing channels deliver the best ROI
- How to optimize pricing for maximum revenue

## Getting Started with Analytics
You don't need a data science degree to benefit from analytics. Start with these simple steps:
1. **Identify Key Metrics**: Focus on 3-5 metrics that directly impact your bottom line
2. **Collect Clean Data**: Ensure your data is accurate and consistent
3. **Use Visual Dashboards**: Make complex data easy to understand at a glance
4. **Act on Insights**: Turn discoveries into concrete business actions

## Three Analytics Solutions Omnilyzer Can Build

### 1. Sales Performance Dashboard
A comprehensive dashboard that tracks sales trends, customer behavior, and revenue patterns. Helps identify your best customers and most profitable products.

### 2. Marketing ROI Tracker
An analytics system that measures the effectiveness of your marketing campaigns across all channels, showing exactly which efforts drive the most revenue.

### 3. Inventory Optimization System
A smart system that predicts demand, optimizes stock levels, and reduces waste while ensuring you never run out of popular items.

Transform your business with data-driven insights. Contact Omnilyzer to build a custom analytics solution tailored to your needs.`,
        author: 'Michael Hansen',
        status: 'published',
        category: 'Business Intelligence',
        tags: ['Analytics', 'Data', 'Business Intelligence', 'SMB'],
        readTime: 6,
        trendTopic: 'small business data analytics',
        publishedAt: '2025-07-04T10:00:00Z',
        createdAt: '2025-07-04T09:30:00Z',
        updatedAt: '2025-07-04T09:45:00Z'
      }
    ];
    await writeBlogPosts(defaultPosts);
    return defaultPosts;
  }
}

async function writeBlogPosts(posts: BlogPost[]) {
  await ensureBlogDataDirectory();
  await fs.writeFile(BLOG_DATA_FILE, JSON.stringify({ 
    posts, 
    lastUpdated: new Date().toISOString() 
  }, null, 2));
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'published';
    const category = searchParams.get('category');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    let posts = await readBlogPosts();

    // Filter by status
    if (status !== 'all') {
      posts = posts.filter(post => post.status === status);
    }

    // Filter by category
    if (category && category !== 'all') {
      posts = posts.filter(post => post.category === category);
    }

    // Sort by published date (newest first)
    posts.sort((a, b) => {
      const dateA = new Date(a.publishedAt || a.createdAt);
      const dateB = new Date(b.publishedAt || b.createdAt);
      return dateB.getTime() - dateA.getTime();
    });

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedPosts = posts.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      data: {
        posts: paginatedPosts,
        pagination: {
          page,
          limit,
          total: posts.length,
          pages: Math.ceil(posts.length / limit)
        }
      }
    });

  } catch (error) {
    console.error('Blog posts error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get blog posts' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { title, summary, content, category, tags, trendTopic, aiPrompt } = body;

    // Validate required fields
    if (!title || !summary || !content) {
      return NextResponse.json(
        { success: false, message: 'Title, summary, and content are required' },
        { status: 400 }
      );
    }

    const posts = await readBlogPosts();
    const nextId = Math.max(...posts.map(p => p.id), 0) + 1;

    // Generate slug from title
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    // Calculate read time (average 200 words per minute)
    const wordCount = content.split(/\s+/).length;
    const readTime = Math.ceil(wordCount / 200);

    const newPost: BlogPost = {
      id: nextId,
      title,
      slug,
      summary,
      content,
      author: 'Michael Hansen',
      status: 'draft',
      category,
      tags: Array.isArray(tags) ? tags : [],
      readTime,
      trendTopic,
      aiPrompt,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    posts.push(newPost);
    await writeBlogPosts(posts);

    return NextResponse.json({
      success: true,
      message: 'Blog post created successfully',
      data: newPost
    });

  } catch (error) {
    console.error('Create blog post error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create blog post' },
      { status: 500 }
    );
  }
}
