import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

const BLOG_DATA_FILE = path.join(process.cwd(), 'data', 'blog-posts.json');

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  summary: string;
  content: string;
  author: string;
  status: 'draft' | 'published' | 'archived';
  category?: string;
  tags?: string[];
  readTime?: number;
  trendTopic?: string;
  aiPrompt?: string;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

async function readBlogPosts(): Promise<BlogPost[]> {
  try {
    const data = await fs.readFile(BLOG_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.posts || [];
  } catch {
    return [];
  }
}

async function writeBlogPosts(posts: BlogPost[]) {
  const dataDir = path.dirname(BLOG_DATA_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
  await fs.writeFile(BLOG_DATA_FILE, JSON.stringify({ 
    posts, 
    lastUpdated: new Date().toISOString() 
  }, null, 2));
}

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const slug = params.slug;
    const posts = await readBlogPosts();
    
    const post = posts.find(p => p.slug === slug);
    
    if (!post) {
      return NextResponse.json(
        { success: false, message: 'Blog post not found' },
        { status: 404 }
      );
    }

    // Only return published posts for public access
    // Admin access would need authentication check here
    if (post.status !== 'published') {
      return NextResponse.json(
        { success: false, message: 'Blog post not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: post
    });

  } catch (error) {
    console.error('Get blog post error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get blog post' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const slug = params.slug;
    const body = await request.json();
    const posts = await readBlogPosts();
    
    const postIndex = posts.findIndex(p => p.slug === slug);
    
    if (postIndex === -1) {
      return NextResponse.json(
        { success: false, message: 'Blog post not found' },
        { status: 404 }
      );
    }

    // Update the post
    const updatedPost = {
      ...posts[postIndex],
      ...body,
      updatedAt: new Date().toISOString()
    };

    // If publishing, set publishedAt
    if (body.status === 'published' && posts[postIndex].status !== 'published') {
      updatedPost.publishedAt = new Date().toISOString();
    }

    posts[postIndex] = updatedPost;
    await writeBlogPosts(posts);

    return NextResponse.json({
      success: true,
      message: 'Blog post updated successfully',
      data: updatedPost
    });

  } catch (error) {
    console.error('Update blog post error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update blog post' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Get the Authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'No valid token provided' },
        { status: 401 }
      );
    }

    const slug = params.slug;
    const posts = await readBlogPosts();
    
    const postIndex = posts.findIndex(p => p.slug === slug);
    
    if (postIndex === -1) {
      return NextResponse.json(
        { success: false, message: 'Blog post not found' },
        { status: 404 }
      );
    }

    // Remove the post
    posts.splice(postIndex, 1);
    await writeBlogPosts(posts);

    return NextResponse.json({
      success: true,
      message: 'Blog post deleted successfully'
    });

  } catch (error) {
    console.error('Delete blog post error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete blog post' },
      { status: 500 }
    );
  }
}
