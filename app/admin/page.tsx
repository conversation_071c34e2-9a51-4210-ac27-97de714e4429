'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../lib/auth-context';
import LoadingSpinner from '../../components/LoadingSpinner';
import AnalyticsDashboard from '../../components/AnalyticsDashboard';
import AnalyticsOverview from '../../components/AnalyticsOverview';
import AdvancedAnalyticsDashboard from '../../components/AdvancedAnalyticsDashboard';
import HealthDashboard from '../../components/HealthDashboard';
import SimpleHealthDashboard from '../../components/SimpleHealthDashboard';
import SystemHealthDashboard from '../../components/SystemHealthDashboard';
import PerformanceDashboard from '../../components/PerformanceDashboard';
import SecurityDashboard from '../../components/SecurityDashboard';
import EnhancedUserManagement from '../../components/EnhancedUserManagement';
import AuthUserManagement from '../../components/admin/AuthUserManagement';
import BlogManagement from '../../components/admin/BlogManagement';
import AdminDebugInfo from '../../components/AdminDebugInfo';
import DatabaseManagement from '../../components/admin/DatabaseManagement';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

interface EmailSettings {
  smtpHost: string;
  smtpPort: number;
  smtpSecure: boolean;
  smtpUser: string;
  smtpPassword: string;
  emailFrom: string;
  emailFromName: string;
  supportEmail: string;
}

export default function AdminDashboard() {
  const router = useRouter();
  const { user, isLoading, hasRole } = useAuth();
  const [adminLoading, setAdminLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [emailSettings, setEmailSettings] = useState<EmailSettings>({
    smtpHost: '',
    smtpPort: 587,
    smtpSecure: false,
    smtpUser: '',
    smtpPassword: '',
    emailFrom: '',
    emailFromName: '',
    supportEmail: ''
  });
  const [testEmailResult, setTestEmailResult] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (!isLoading) {
      if (!user) {
        router.push('/login');
        return;
      }

      if (!hasRole(['Admin', 'Developer'])) {
        router.push('/dashboard');
        return;
      }

      setAdminLoading(false);
    }
  }, [user, isLoading, hasRole, router]);

  useEffect(() => {
    if (activeTab === 'email' && !adminLoading) {
      loadEmailSettings();
    }
  }, [activeTab, adminLoading]);



  const loadEmailSettings = async () => {
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/v1/admin/settings', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.settings && data.settings.email) {
          setEmailSettings(data.settings.email);
        }
      }
    } catch (error) {
      console.error('Failed to load email settings:', error);
    }
  };

  const saveEmailSettings = async () => {
    setSaving(true);
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/v1/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          email: emailSettings
        }),
      });

      if (response.ok) {
        alert('Email settings saved successfully!');
      } else {
        alert('Failed to save email settings');
      }
    } catch (error) {
      console.error('Failed to save email settings:', error);
      alert('Failed to save email settings');
    } finally {
      setSaving(false);
    }
  };

  const testEmailConfiguration = async () => {
    setTestEmailResult('Testing...');
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/v1/admin/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          to: user?.email,
          settings: emailSettings
        }),
      });

      const result = await response.json();
      if (response.ok) {
        setTestEmailResult('✅ Test email sent successfully!');
      } else {
        setTestEmailResult(`❌ Test failed: ${result.message}`);
      }
    } catch (error) {
      setTestEmailResult('❌ Test failed: Network error');
    }
  };

  const logout = () => {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    router.push('/');
  };

  if (isLoading || adminLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Redirect if not authenticated or not admin
  if (!user || !hasRole(['Admin', 'Developer'])) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, {user?.name}</span>
              <button
                onClick={logout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Debug Information */}
        <AdminDebugInfo />

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', name: 'Overview' },
              { id: 'analytics', name: 'Analytics' },
              { id: 'advanced-analytics', name: 'Advanced Analytics' },
              { id: 'health', name: 'System Health' },
              { id: 'performance', name: 'Performance' },
              { id: 'security', name: 'Security' },
              { id: 'email', name: 'Email Configuration' },
              { id: 'users', name: 'User Management' },
              { id: 'auth-users', name: 'Login Users' },
              { id: 'blog', name: 'Blog Management' },
              { id: 'database', name: 'Database Management' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <AnalyticsOverview />
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">System Status</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-green-800">Contact Form</p>
                      <p className="text-xs text-green-600">Working</p>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-green-800">Database</p>
                      <p className="text-xs text-green-600">Connected</p>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-green-800">Authentication</p>
                      <p className="text-xs text-green-600">Active</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={() => setActiveTab('email')}
                  className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left"
                >
                  <h3 className="font-medium text-gray-900">Configure Email</h3>
                  <p className="text-sm text-gray-600">Set up SMTP settings for contact form</p>
                </button>
                <button
                  onClick={() => setActiveTab('users')}
                  className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left"
                >
                  <h3 className="font-medium text-gray-900">Manage Users</h3>
                  <p className="text-sm text-gray-600">View and manage user accounts</p>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <AnalyticsDashboard />
        )}

        {/* Advanced Analytics Tab */}
        {activeTab === 'advanced-analytics' && (
          <AdvancedAnalyticsDashboard />
        )}

        {/* System Health Tab */}
        {activeTab === 'health' && (
          <SystemHealthDashboard />
        )}

        {/* Performance Tab */}
        {activeTab === 'performance' && (
          <PerformanceDashboard />
        )}

        {/* Security Tab */}
        {activeTab === 'security' && (
          <SecurityDashboard />
        )}

        {/* Email Configuration Tab */}
        {activeTab === 'email' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Email Configuration</h2>

            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SMTP Host
                  </label>
                  <input
                    type="text"
                    value={emailSettings.smtpHost}
                    onChange={(e) => setEmailSettings({ ...emailSettings, smtpHost: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="smtp.gmail.com"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SMTP Port
                  </label>
                  <input
                    type="number"
                    value={emailSettings.smtpPort}
                    onChange={(e) => setEmailSettings({ ...emailSettings, smtpPort: parseInt(e.target.value) })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={emailSettings.smtpSecure}
                  onChange={(e) => setEmailSettings({ ...emailSettings, smtpSecure: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-700">
                  Use SSL/TLS (recommended for port 465)
                </label>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SMTP Username
                  </label>
                  <input
                    type="text"
                    value={emailSettings.smtpUser}
                    onChange={(e) => setEmailSettings({ ...emailSettings, smtpUser: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SMTP Password
                  </label>
                  <input
                    type="password"
                    value={emailSettings.smtpPassword}
                    onChange={(e) => setEmailSettings({ ...emailSettings, smtpPassword: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="App password or SMTP password"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    From Email
                  </label>
                  <input
                    type="email"
                    value={emailSettings.emailFrom}
                    onChange={(e) => setEmailSettings({ ...emailSettings, emailFrom: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    From Name
                  </label>
                  <input
                    type="text"
                    value={emailSettings.emailFromName}
                    onChange={(e) => setEmailSettings({ ...emailSettings, emailFromName: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Omnilyzer"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Support Email (where contact form submissions are sent)
                </label>
                <input
                  type="email"
                  value={emailSettings.supportEmail}
                  onChange={(e) => setEmailSettings({ ...emailSettings, supportEmail: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="flex space-x-4">
                <button
                  onClick={saveEmailSettings}
                  disabled={saving}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium transition-colors disabled:opacity-50"
                >
                  {saving ? 'Saving...' : 'Save Settings'}
                </button>

                <button
                  onClick={testEmailConfiguration}
                  className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md font-medium transition-colors"
                >
                  Test Email
                </button>
              </div>

              {testEmailResult && (
                <div className={`p-4 rounded-md ${testEmailResult.includes('✅') ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                  {testEmailResult}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Users Tab */}
        {activeTab === 'users' && (
          <EnhancedUserManagement />
        )}

        {/* Auth Users Tab */}
        {activeTab === 'auth-users' && (
          <AuthUserManagement />
        )}

        {/* Blog Management Tab */}
        {activeTab === 'blog' && (
          <BlogManagement />
        )}

        {/* Database Management Tab */}
        {activeTab === 'database' && (
          <DatabaseManagement />
        )}
      </div>
    </div>
  );
}
