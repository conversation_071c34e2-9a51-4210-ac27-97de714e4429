'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

export interface User {
  id: number;
  name: string;
  email: string;
  role: 'Admin' | 'Developer' | 'Reviewer' | 'Guest';
  profileImage?: string;
  emailVerified: boolean;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; message: string }>;
  loginWithGoogle: () => void;
  logout: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string | string[]) => boolean;
  setUser: (user: User | null) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Role permissions mapping (should match server-side)
const ROLE_PERMISSIONS = {
  Admin: ['*'], // Admin has all permissions
  Developer: [
    'VIEW_USERS', 'VIEW_CONTENT', 'CREATE_CONTENT', 'UPDATE_CONTENT',
    'VIEW_SETTINGS', 'VIEW_ANALYTICS', 'EXPORT_ANALYTICS', 'ACCESS_API_DOCS',
    'RUN_DIAGNOSTICS', 'VIEW_LOGS', 'UPDATE_OWN_PROFILE', 'VIEW_DASHBOARD'
  ],
  Reviewer: [
    'VIEW_CONTENT', 'CREATE_CONTENT', 'UPDATE_CONTENT', 'VIEW_ANALYTICS',
    'UPDATE_OWN_PROFILE', 'VIEW_DASHBOARD'
  ],
  Guest: [
    'VIEW_CONTENT', 'UPDATE_OWN_PROFILE', 'VIEW_DASHBOARD'
  ]
};

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Check if user has specific permission
  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    const userPermissions = ROLE_PERMISSIONS[user.role] || [];
    return userPermissions.includes('*') || userPermissions.includes(permission);
  };

  // Check if user has specific role(s)
  const hasRole = (role: string | string[]): boolean => {
    if (!user) return false;
    if (Array.isArray(role)) {
      return role.includes(user.role);
    }
    return user.role === role;
  };

  // Get stored token
  const getToken = (): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('accessToken');
  };

  // Store token
  const setToken = (token: string): void => {
    if (typeof window === 'undefined') return;
    localStorage.setItem('accessToken', token);
  };

  // Remove token
  const removeToken = (): void => {
    if (typeof window === 'undefined') return;
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  };

  // API call helper with auth header
  const apiCall = async (url: string, options: RequestInit = {}): Promise<Response> => {
    const token = getToken();
    const headers = {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    };

    return fetch(url, {
      ...options,
      headers,
    });
  };

  // Login function
  const login = async (email: string, password: string): Promise<{ success: boolean; message: string }> => {
    try {
      setIsLoading(true);

      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setToken(data.accessToken);
        if (data.refreshToken) {
          localStorage.setItem('refreshToken', data.refreshToken);
        }
        setUser(data.user);
        return { success: true, message: 'Login successful' };
      } else {
        return { success: false, message: data.message || 'Login failed' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, message: 'Network error. Please try again.' };
    } finally {
      setIsLoading(false);
    }
  };

  // Google OAuth login - New approach using backend endpoint that returns JSON
  const loginWithGoogle = async (): Promise<void> => {
    console.log('🚀 loginWithGoogle function called');
    try {
      const apiUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/v1/auth/google-url`;
      console.log('🔗 Calling OAuth URL endpoint:', apiUrl);

      // Get the OAuth URL from backend as JSON instead of redirect
      const response = await fetch(apiUrl);
      const data = await response.json();

      if (data.success && data.oauthUrl) {
        window.location.href = data.oauthUrl;
      } else {
        console.error('Failed to get OAuth URL:', data);
        // Fallback to direct URL
        const oauthUrl = 'https://accounts.google.com/o/oauth2/v2/auth?response_type=code&redirect_uri=https%3A%2F%2Fdev.omnilyzer.ai%2Fapi%2Fv1%2Fauth%2Fgoogle%2Fcallback&scope=profile%20email&client_id=***********-4v5298gf1nq5bl48sblfiq7mvaf5chg3.apps.googleusercontent.com';
        window.location.href = oauthUrl;
      }
    } catch (error) {
      console.error('OAuth error:', error);
      // Fallback to direct URL
      const oauthUrl = 'https://accounts.google.com/o/oauth2/v2/auth?response_type=code&redirect_uri=https%3A%2F%2Fdev.omnilyzer.ai%2Fapi%2Fv1%2Fauth%2Fgoogle%2Fcallback&scope=profile%20email&client_id=***********-4v5298gf1nq5bl48sblfiq7mvaf5chg3.apps.googleusercontent.com';
      window.location.href = oauthUrl;
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      const token = getToken();
      if (token) {
        // Call logout endpoint to invalidate session on server
        await apiCall('/api/v1/auth/logout', {
          method: 'POST',
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      removeToken();
      setUser(null);
    }
  };

  // Refresh token function
  const refreshToken = async (): Promise<boolean> => {
    try {
      const refreshTokenValue = localStorage.getItem('refreshToken');
      if (!refreshTokenValue) return false;

      const response = await fetch('/api/v1/auth/refresh-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken: refreshTokenValue }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setToken(data.accessToken);
        if (data.refreshToken) {
          localStorage.setItem('refreshToken', data.refreshToken);
        }
        return true;
      } else {
        removeToken();
        setUser(null);
        return false;
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      removeToken();
      setUser(null);
      return false;
    }
  };

  // Get current user info
  const getCurrentUser = async (): Promise<void> => {
    try {
      const token = getToken();
      if (!token) {
        setIsLoading(false);
        return;
      }

      const response = await apiCall('/api/v1/auth/me');
      const data = await response.json();

      if (response.ok && data.success) {
        setUser(data.user);
      } else if (response.status === 401) {
        // Token expired, try to refresh
        const refreshed = await refreshToken();
        if (refreshed) {
          // Retry getting user info
          const retryResponse = await apiCall('/api/v1/auth/me');
          const retryData = await retryResponse.json();
          if (retryResponse.ok && retryData.success) {
            setUser(retryData.user);
          } else {
            removeToken();
          }
        } else {
          removeToken();
        }
      } else {
        removeToken();
      }
    } catch (error) {
      console.error('Get current user error:', error);
      removeToken();
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize auth state on mount
  useEffect(() => {
    getCurrentUser();
  }, []);

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    loginWithGoogle,
    logout,
    refreshToken,
    hasPermission,
    hasRole,
    setUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
