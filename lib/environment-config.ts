/**
 * Environment Configuration Utility
 * Centralizes all environment-specific configuration
 */

export const ENV = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production',
} as const;

export const APP_CONFIG = {
  // Application URLs
  APP_URL: process.env.NEXT_PUBLIC_APP_URL || (
    ENV.IS_PRODUCTION 
      ? 'https://omnilyzer.ai' 
      : 'https://dev.omnilyzer.ai'
  ),
  API_URL: process.env.NEXT_PUBLIC_API_URL || (
    ENV.IS_PRODUCTION 
      ? 'https://omnilyzer.ai/api' 
      : 'https://dev.omnilyzer.ai/api'
  ),
  
  // Server Configuration
  PORT: parseInt(process.env.PORT || '3002'),
  
  // Database
  DATABASE_URL: process.env.DATABASE_URL || (
    ENV.IS_PRODUCTION
      ? 'postgresql://localhost:5432/omnilyzer_prod'
      : 'postgresql://localhost:5432/omnilyzer_dev'
  ),
  DATABASE_POOL_SIZE: parseInt(process.env.DATABASE_POOL_SIZE || (ENV.IS_PRODUCTION ? '20' : '10')),
} as const;

export const ANALYTICS_CONFIG = {
  // Analytics should work in both environments but with different settings
  ENABLED: process.env.ANALYTICS_ENABLED === 'true' || ENV.IS_PRODUCTION,
  DEBUG: process.env.ANALYTICS_DEBUG === 'true' || ENV.IS_DEVELOPMENT,
  
  // Google Analytics
  GA_ID: process.env.NEXT_PUBLIC_GA_ID || (
    ENV.IS_PRODUCTION 
      ? 'G-YYYYYYYYYY' // Replace with actual production GA ID
      : 'G-XXXXXXXXXX' // Replace with actual development GA ID
  ),
  
  // Advanced Analytics
  ADVANCED_ENABLED: process.env.ENABLE_ADVANCED_ANALYTICS !== 'false',
  HEATMAPS_ENABLED: process.env.ENABLE_HEATMAPS !== 'false',
} as const;

export const EMAIL_CONFIG = {
  SMTP_HOST: process.env.SMTP_HOST || (
    ENV.IS_PRODUCTION 
      ? 'send.one.com' 
      : 'smtp.mailtrap.io'
  ),
  SMTP_PORT: parseInt(process.env.SMTP_PORT || (ENV.IS_PRODUCTION ? '2525' : '2525')),
  SMTP_USER: process.env.SMTP_USER || '',
  SMTP_PASS: process.env.SMTP_PASS || '',
  SMTP_FROM: process.env.SMTP_FROM || (
    ENV.IS_PRODUCTION 
      ? '<EMAIL>' 
      : '<EMAIL>'
  ),
} as const;

export const AUTH_CONFIG = {
  JWT_SECRET: process.env.JWT_SECRET || 'fallback-jwt-secret-change-in-production',
  SESSION_SECRET: process.env.SESSION_SECRET || 'fallback-session-secret-change-in-production',
  
  // Google OAuth
  GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID || '',
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET || '',
} as const;

export const RATE_LIMIT_CONFIG = {
  WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000'),
  MAX_REQUESTS: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || (ENV.IS_PRODUCTION ? '100' : '1000')),
} as const;

export const FEATURE_FLAGS = {
  CHATBOT_ENABLED: process.env.ENABLE_CHATBOT !== 'false',
  ADVANCED_ANALYTICS: process.env.ENABLE_ADVANCED_ANALYTICS !== 'false',
  HEATMAPS: process.env.ENABLE_HEATMAPS !== 'false',
  DEV_TOOLS: process.env.ENABLE_DEV_TOOLS === 'true' || ENV.IS_DEVELOPMENT,
} as const;

export const LOGGING_CONFIG = {
  LEVEL: process.env.LOG_LEVEL || (ENV.IS_PRODUCTION ? 'info' : 'debug'),
  TO_FILE: process.env.LOG_TO_FILE === 'true',
} as const;

/**
 * Utility function to get environment-specific values
 */
export function getEnvValue<T>(productionValue: T, developmentValue: T): T {
  return ENV.IS_PRODUCTION ? productionValue : developmentValue;
}

/**
 * Utility function to check if a feature is enabled
 */
export function isFeatureEnabled(feature: keyof typeof FEATURE_FLAGS): boolean {
  return FEATURE_FLAGS[feature];
}

/**
 * Utility function to get the appropriate API endpoint
 */
export function getApiEndpoint(path: string): string {
  return `${APP_CONFIG.API_URL}${path.startsWith('/') ? path : `/${path}`}`;
}

/**
 * Utility function to log environment-aware messages
 */
export function envLog(message: string, level: 'info' | 'warn' | 'error' | 'debug' = 'info'): void {
  if (level === 'debug' && ENV.IS_PRODUCTION) return;
  
  const prefix = ENV.IS_PRODUCTION ? '[PROD]' : '[DEV]';
  console[level](`${prefix} ${message}`);
}

/**
 * Configuration validation - run this at startup
 */
export function validateEnvironmentConfig(): void {
  const requiredVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'SESSION_SECRET',
  ];
  
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.error('Missing required environment variables:', missing);
    if (ENV.IS_PRODUCTION) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    } else {
      console.warn('Using fallback values for development');
    }
  }
  
  envLog(`Environment configuration loaded for ${ENV.NODE_ENV} mode`);
}
