/**
 * Frontend Logger - Fixed version without infinite loops
 */

interface LogEntry {
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  timestamp: string;
  url: string;
  userAgent: string;
  userId?: number;
  stack?: string;
  component?: string;
  props?: any;
  extra?: any;
}

class FrontendLogger {
  private logQueue: LogEntry[] = [];
  private isOnline = true;
  private maxQueueSize = 100;
  private flushInterval = 5000;
  private endpoint = '/api/v1/logs/frontend';
  private originalConsole: any = {};

  constructor() {
    if (typeof window !== 'undefined') {
      this.preserveOriginalConsole();
      this.setupErrorHandlers();
      this.setupPeriodicFlush();
      this.setupOnlineStatusTracking();
    }
  }

  private preserveOriginalConsole() {
    // Store original console methods to prevent infinite loops
    this.originalConsole = {
      error: console.error.bind(console),
      warn: console.warn.bind(console),
      info: console.info.bind(console),
      log: console.log.bind(console),
      debug: console.debug.bind(console)
    };
  }

  private setupErrorHandlers() {
    if (typeof window === 'undefined') return;

    // Global error handler
    window.addEventListener('error', (event) => {
      this.logError({
        message: event.message,
        stack: event.error?.stack,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        type: 'javascript_error'
      });
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        type: 'promise_rejection'
      });
    });
  }

  private setupOnlineStatusTracking() {
    if (typeof window === 'undefined') return;

    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushLogs();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  private setupPeriodicFlush() {
    if (typeof window === 'undefined') return;

    setInterval(() => {
      this.flushLogs();
    }, this.flushInterval);
  }

  private createLogEntry(level: LogEntry['level'], data: any): LogEntry {
    return {
      level,
      message: typeof data === 'string' ? data : data.message || 'Unknown error',
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : '',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
      stack: data.stack,
      component: data.component,
      props: data.props,
      extra: data.extra || data,
      userId: this.getCurrentUserId()
    };
  }

  private getCurrentUserId(): number | undefined {
    if (typeof window === 'undefined') return undefined;

    try {
      const user = localStorage.getItem('user');
      return user ? JSON.parse(user).id : undefined;
    } catch {
      return undefined;
    }
  }

  private addToQueue(entry: LogEntry) {
    this.logQueue.push(entry);

    if (this.logQueue.length > this.maxQueueSize) {
      this.logQueue = this.logQueue.slice(-this.maxQueueSize);
    }
  }

  logError(data: any) {
    const entry = this.createLogEntry('error', data);
    this.addToQueue(entry);

    // Use original console methods to prevent infinite loops
    if (process.env.NODE_ENV === 'development') {
      this.originalConsole.error('🚨 Frontend Error:', entry.message);
      if (entry.stack) {
        this.originalConsole.error('Stack:', entry.stack);
      }
    }
  }

  logWarn(data: any) {
    const entry = this.createLogEntry('warn', data);
    this.addToQueue(entry);

    if (process.env.NODE_ENV === 'development') {
      this.originalConsole.warn('⚠️ Frontend Warning:', entry.message);
    }
  }

  logInfo(data: any) {
    const entry = this.createLogEntry('info', data);
    this.addToQueue(entry);

    if (process.env.NODE_ENV === 'development') {
      this.originalConsole.info('ℹ️ Frontend Info:', entry.message);
    }
  }

  logDebug(data: any) {
    const entry = this.createLogEntry('debug', data);
    this.addToQueue(entry);

    if (process.env.NODE_ENV === 'development') {
      this.originalConsole.debug('🐛 Frontend Debug:', entry.message);
    }
  }

  private async flushLogs() {
    if (!this.isOnline || this.logQueue.length === 0) return;

    const logsToSend = [...this.logQueue];
    this.logQueue = [];

    try {
      const response = await fetch(this.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ logs: logsToSend })
      });

      if (!response.ok) {
        // Silently fail - don't re-add logs to prevent infinite loops
        if (process.env.NODE_ENV === 'development') {
          this.originalConsole.warn('Frontend logger: Failed to send logs, status:', response.status);
        }
      }
    } catch (error) {
      // Silently fail - don't re-add logs to prevent infinite loops
      if (process.env.NODE_ENV === 'development') {
        this.originalConsole.warn('Frontend logger: Network error sending logs:', error.message);
      }
    }
  }

  // Performance logging
  logPerformance(metric: string, value: number, extra?: any) {
    const entry = this.createLogEntry('info', {
      message: `Performance: ${metric}`,
      metric,
      value,
      extra,
      type: 'performance'
    });
    this.addToQueue(entry);

    if (process.env.NODE_ENV === 'development') {
      this.originalConsole.info(`⚡ Performance: ${metric} = ${value}ms`, extra);
    }
  }

  // React error logging
  logReactError(error: Error, errorInfo: any, component?: string) {
    const entry = this.createLogEntry('error', {
      message: `React Error in ${component || 'Unknown Component'}: ${error.message}`,
      stack: error.stack,
      component,
      errorInfo,
      type: 'react_error'
    });
    this.addToQueue(entry);

    if (process.env.NODE_ENV === 'development') {
      this.originalConsole.error(`⚛️ React Error in ${component}:`, error);
      this.originalConsole.error('Error Info:', errorInfo);
    }
  }

  // Public methods for manual logging
  error(message: string, extra?: any) {
    this.logError({ message, extra });
  }

  warn(message: string, extra?: any) {
    this.logWarn({ message, extra });
  }

  info(message: string, extra?: any) {
    this.logInfo({ message, extra });
  }

  debug(message: string, extra?: any) {
    this.logDebug({ message, extra });
  }
}

// Create singleton instance
const frontendLogger = new FrontendLogger();

// Export for use in components
export default frontendLogger;

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).__FRONTEND_LOGGER__ = frontendLogger;
}
