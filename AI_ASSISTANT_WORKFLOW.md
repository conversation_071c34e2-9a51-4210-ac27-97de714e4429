# AI Assistant Dual-Mode Development Workflow

## 🤖 For Claude/AI Assistant: MA<PERSON><PERSON>ORY WORKFLOW

### **RULE #1: ALWAYS START IN DEVELOPMENT**
```bash
# FIRST COMMAND in any development session
cd /var/www/omnilyzer
```

### **RULE #2: CHECK ENVIRONMENT BEFORE CHANGES**
```bash
# SECOND COMMAND - verify you're in the right place
/var/www/dev_workflow_helper.sh status
```

### **RULE #3: DEVELOPMENT-FIRST WORKFLOW**

#### Step 1: Verify Development Environment
```bash
cd /var/www/omnilyzer
/var/www/dev_workflow_helper.sh status
```

#### Step 2: Make Changes in Development
- Edit files in `/var/www/omnilyzer/`
- Test changes on `https://dev.omnilyzer.ai`
- Verify functionality works

#### Step 3: Test Development Environment
```bash
/var/www/dev_workflow_helper.sh test
```

#### Step 4: Check Deployment Readiness
```bash
/var/www/dev_workflow_helper.sh ready
```

#### Step 5: Deploy to Production (Only if ready)
```bash
/var/www/deploy_to_production.sh
```

## 🚨 CRITICAL REMINDERS FOR AI ASSISTANT

### **NEVER DO THESE:**
- ❌ Edit files directly in `/var/www/omnilyzer-prod/`
- ❌ Make changes without testing in development first
- ❌ Deploy without running readiness checks
- ❌ Work in production directory

### **ALWAYS DO THESE:**
- ✅ Start in `/var/www/omnilyzer/`
- ✅ Use `codebase-retrieval` to understand existing code
- ✅ Test changes on development server first
- ✅ Use deployment script for production updates
- ✅ Verify both environments after deployment

## 📋 QUICK REFERENCE COMMANDS

### Environment Check:
```bash
cd /var/www/omnilyzer
/var/www/dev_workflow_helper.sh status
```

### Start Development Services:
```bash
cd /var/www/omnilyzer
/var/www/dev_workflow_helper.sh start
```

### Test Development:
```bash
cd /var/www/omnilyzer
/var/www/dev_workflow_helper.sh test
```

### Deploy to Production:
```bash
cd /var/www/omnilyzer
/var/www/dev_workflow_helper.sh deploy
```

### Emergency Restart:
```bash
cd /var/www
./restart_omnilyzer.sh
```

## 🔄 TYPICAL AI ASSISTANT SESSION

```bash
# 1. Always start here
cd /var/www/omnilyzer

# 2. Check current status
/var/www/dev_workflow_helper.sh status

# 3. If services not running, start them
/var/www/dev_workflow_helper.sh start

# 4. Make your changes (edit files, etc.)
# ... development work ...

# 5. Test your changes
/var/www/dev_workflow_helper.sh test

# 6. Check if ready for deployment
/var/www/dev_workflow_helper.sh ready

# 7. Deploy to production
/var/www/dev_workflow_helper.sh deploy
```

## 🎯 ENVIRONMENT-SPECIFIC CONFIGURATIONS

### Development Environment:
- **Directory**: `/var/www/omnilyzer/`
- **URL**: `https://dev.omnilyzer.ai`
- **Ports**: 3000 (Frontend), 3001 (Backend)
- **NODE_ENV**: `development`
- **Purpose**: All development and testing

### Production Environment:
- **Directory**: `/var/www/omnilyzer-prod/`
- **URL**: `https://omnilyzer.ai`
- **Port**: 3002
- **NODE_ENV**: `production`
- **Purpose**: Live website (deploy only)

## 🛠️ FILE EDITING WORKFLOW

### When editing files:
1. **Always** use `codebase-retrieval` first to understand existing code
2. **Always** edit in `/var/www/omnilyzer/` (development)
3. **Test** changes on development server
4. **Deploy** using the deployment script

### Example:
```bash
# 1. Start in development
cd /var/www/omnilyzer

# 2. Use codebase retrieval to understand code
# (AI Assistant uses codebase-retrieval tool)

# 3. Edit files using str-replace-editor
# (AI Assistant edits files in development)

# 4. Test changes
/var/www/dev_workflow_helper.sh test

# 5. Deploy to production
/var/www/dev_workflow_helper.sh deploy
```

## 🚨 ERROR HANDLING

### If Development Server Issues:
```bash
cd /var/www/omnilyzer
/var/www/dev_workflow_helper.sh start
```

### If Production Server Issues:
```bash
# Fix in development first, then deploy
cd /var/www/omnilyzer
# Fix the issue
/var/www/dev_workflow_helper.sh deploy
```

### If Deployment Fails:
```bash
# Automatic rollback is built into deployment script
# Or manual rollback:
/var/www/deploy_to_production.sh rollback
```

## 📝 LOGGING AND MONITORING

### Development Logs:
- Frontend: `/var/www/logs/omnilyzer-dev.log`
- Backend: `/var/www/logs/omnilyzer-backend.log`

### Production Logs:
- Frontend: `/var/www/logs/omnilyzer-prod.log`

### Deployment Logs:
- Deployment: `/var/www/logs/deployment.log`

### Check Logs:
```bash
# Development
tail -f /var/www/logs/omnilyzer-dev.log

# Production
tail -f /var/www/logs/omnilyzer-prod.log

# Deployment
tail -f /var/www/logs/deployment.log
```

## ✅ SUCCESS INDICATORS

### Development Ready:
- ✅ Working in `/var/www/omnilyzer/`
- ✅ Development server responding
- ✅ All APIs working
- ✅ No console errors

### Production Ready:
- ✅ Development tests pass
- ✅ Deployment script succeeds
- ✅ Production server responding
- ✅ All features working

This workflow ensures consistent, safe development practices and prevents production issues!
