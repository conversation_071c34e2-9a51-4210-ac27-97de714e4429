/** @type {import('next').NextConfig} */
const nextConfig = {
  // Basic configuration without experimental features that might interfere with CSS
  reactStrictMode: false,

  // Skip TypeScript checking during build
  typescript: {
    ignoreBuildErrors: true,
  },

  // Skip ESLint during build
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Images configuration
  images: {
    unoptimized: true,
    domains: ['*************', 'dev.omnilyzer.ai', 'omnilyzer.ai'],
  },
}

module.exports = nextConfig
