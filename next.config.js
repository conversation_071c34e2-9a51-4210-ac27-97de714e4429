/** @type {import('next').NextConfig} */
const nextConfig = {
  // Basic configuration without experimental features that might interfere with CSS
  reactStrictMode: false,

  // Skip TypeScript checking during build
  typescript: {
    ignoreBuildErrors: true,
  },

  // Skip ESLint during build
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Images configuration
  images: {
    unoptimized: true,
    domains: ['*************', 'dev.omnilyzer.ai', 'omnilyzer.ai'],
  },

  // Webpack configuration to ensure PostCSS is used for CSS processing
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Find the CSS rule and ensure it uses PostCSS
    const cssRule = config.module.rules.find(
      rule => rule.test && rule.test.toString().includes('css')
    );

    if (cssRule) {
      console.log('Found CSS rule, ensuring PostCSS is configured');
    }

    return config;
  },
}

module.exports = nextConfig
