/**
 * PM2 Ecosystem Configuration
 * This file defines how PM2 should run the Omnilyzer application
 */

module.exports = {
  apps: [
    {
      name: 'omnilyzer',
      script: 'server/index.js',
      instances: 'max',
      exec_mode: 'cluster',
      watch: false,
      env: {
        NODE_ENV: 'production',
      },
      env_production: {
        NODE_ENV: 'production',
      },
      env_development: {
        NODE_ENV: 'development',
      },
      time: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      combine_logs: true,
      error_file: './logs/pm2_error.log',
      out_file: './logs/pm2_output.log',
      merge_logs: true,
      max_memory_restart: '1G',
      autorestart: true,
      restart_delay: 1000,
      wait_ready: true,
      listen_timeout: 50000,
      kill_timeout: 5000,
      post_start: './scripts/startup_log_review.sh',
    },
    {
      name: 'omnilyzer-dev',
      script: 'server/index.js',
      instances: 1,
      exec_mode: 'fork',
      watch: ['server'],
      ignore_watch: ['node_modules', 'logs', '.git', 'public'],
      env: {
        NODE_ENV: 'development',
      },
      time: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      combine_logs: true,
      error_file: './logs/dev_error.log',
      out_file: './logs/dev_output.log'
    },
    {
      name: 'omnilyzer-frontend',
      script: 'npm',
      args: 'run dev',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      env: {
        NODE_ENV: 'development',
        PORT: '3000'
      },
      time: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      combine_logs: true,
      error_file: './logs/frontend_error.log',
      out_file: './logs/frontend_output.log',
      autorestart: true,
      max_memory_restart: '2G',
      restart_delay: 5000,
      max_restarts: 10,
      min_uptime: '10s'
    },
    {
      name: 'omnilyzer-production',
      script: 'npm',
      args: 'run start -- -p 3002',
      cwd: '/var/www/omnilyzer-prod',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      env: {
        NODE_ENV: 'production',
        PORT: '3002'
      },
      time: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      combine_logs: true,
      error_file: './logs/production_error.log',
      out_file: './logs/production_output.log',
      autorestart: true,
      max_memory_restart: '2G',
      restart_delay: 5000,
      max_restarts: 10,
      min_uptime: '10s'
    }
  ],
};
