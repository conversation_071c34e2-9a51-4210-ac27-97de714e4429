# Omnilyzer Production Environment Configuration
# Copy this file to .env.production and customize values

# Environment
NODE_ENV=production

# Application URLs
NEXT_PUBLIC_APP_URL=https://omnilyzer.ai
NEXT_PUBLIC_API_URL=https://omnilyzer.ai/api

# Database Configuration
DATABASE_URL=postgresql://localhost:5432/omnilyzer_prod
DATABASE_POOL_SIZE=20

# Analytics Configuration
NEXT_PUBLIC_GA_ID=G-YYYYYYYYYY
ANALYTICS_ENABLED=true
ANALYTICS_DEBUG=false

# Email Configuration (Production)
SMTP_HOST=send.one.com
SMTP_PORT=2525
SMTP_USER=<EMAIL>
SMTP_PASS=kbtu_dujl_gzsy_mhng
SMTP_FROM=<EMAIL>

# Authentication
JWT_SECRET=your_production_jwt_secret_here
SESSION_SECRET=your_production_session_secret_here

# External APIs (Production keys)
GOOGLE_CLIENT_ID=your_prod_google_client_id
GOOGLE_CLIENT_SECRET=your_prod_google_client_secret

# Rate Limiting (Stricter for production)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_TO_FILE=true

# Feature Flags
ENABLE_CHATBOT=true
ENABLE_ADVANCED_ANALYTICS=true
ENABLE_HEATMAPS=true

# Production-specific
DISABLE_SSL_VERIFY=false
ENABLE_DEV_TOOLS=false

# Performance
ENABLE_COMPRESSION=true
ENABLE_CACHING=true
