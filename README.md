# 🚀 Omnilyzer - B2B Analytics & Business Intelligence Platform

[![Next.js](https://img.shields.io/badge/Next.js-14-black)](https://nextjs.org/)
[![Node.js](https://img.shields.io/badge/Node.js-18-green)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5-blue)](https://www.typescriptlang.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

**Omnilyzer** is a production-ready B2B analytics and business intelligence platform that provides real-time insights, comprehensive user tracking, and powerful admin tools for modern businesses.

## ✨ Features

### 📊 **Real-Time Analytics**
- **Authentic Data Tracking** - Real page views, user behavior, and conversion metrics
- **Live Dashboard** - Real-time visitor tracking and engagement analytics
- **Advanced Metrics** - Bounce rates, session duration, user flow analysis
- **Custom Events** - Track specific business actions and conversions

### 🎯 **Business Intelligence**
- **User Segmentation** - Advanced user behavior analysis and segmentation
- **Conversion Funnels** - Track user journeys and optimize conversion paths
- **Performance Monitoring** - System health, API performance, and uptime tracking
- **Predictive Analytics** - AI-powered insights and trend analysis

### 🛡️ **Admin Dashboard**
- **User Management** - Complete user authentication and authorization system
- **Content Management** - Blog creation, editing, and social media publishing
- **System Monitoring** - Real-time server health and performance metrics
- **Security Auditing** - Comprehensive security monitoring and alerts

### 🌐 **Modern Web Platform**
- **Responsive Design** - Mobile-first, fully responsive user interface
- **SEO Optimized** - Built-in SEO tools and optimization features
- **Social Integration** - Facebook and LinkedIn publishing capabilities
- **API-First** - RESTful APIs for all platform functionality

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Git

### Installation

```bash
# Clone the repository
git clone https://github.com/MichaelTrueX/omnilyzer.git
cd omnilyzer

# Install dependencies
npm install

# Set up environment variables
cp .env.development.template .env.development.local
cp .env.production.template .env.production.local

# Start development server
npm run dev
```

### Environment Setup

Create your environment files with the following variables:

```env
# Database
DATABASE_URL="your_database_url"

# Authentication
NEXTAUTH_SECRET="your_nextauth_secret"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth
GOOGLE_CLIENT_ID="your_google_client_id"
GOOGLE_CLIENT_SECRET="your_google_client_secret"

# Email Service (Amazon SES)
AWS_ACCESS_KEY_ID="your_aws_access_key"
AWS_SECRET_ACCESS_KEY="your_aws_secret_key"
AWS_REGION="us-east-1"

# Social Media
FACEBOOK_ACCESS_TOKEN="your_facebook_token"
LINKEDIN_ACCESS_TOKEN="your_linkedin_token"
```

## 📁 Project Structure

```
omnilyzer/
├── app/                    # Next.js 14 App Router
│   ├── api/               # API routes
│   ├── admin/             # Admin dashboard
│   ├── blog/              # Blog system
│   └── services/          # Service pages
├── components/            # React components
│   ├── admin/             # Admin-specific components
│   └── analytics/         # Analytics components
├── server/                # Express.js backend
│   ├── controllers/       # API controllers
│   ├── middleware/        # Custom middleware
│   └── routes/            # API routes
├── data/                  # JSON data storage
├── docs/                  # Documentation
└── scripts/               # Utility scripts
```

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run dev:backend      # Start backend only
npm run dev:frontend     # Start frontend only

# Production
npm run build           # Build for production
npm run start           # Start production server

# Testing
npm test               # Run tests
npm run test:watch     # Run tests in watch mode

# Utilities
npm run lint           # Run ESLint
npm run type-check     # TypeScript type checking
```

### Development Workflow

1. **Start Development Environment**
   ```bash
   npm run dev
   ```

2. **Access Applications**
   - Frontend: `http://localhost:3000`
   - Backend API: `http://localhost:3001`
   - Admin Dashboard: `http://localhost:3000/admin`

3. **View Analytics**
   - Real-time analytics at `/admin`
   - API endpoints at `/api/v1/analytics/*`

## 🌐 Deployment

### Production Deployment

1. **Build the Application**
   ```bash
   npm run build
   ```

2. **Set Production Environment**
   ```bash
   export NODE_ENV=production
   ```

3. **Start Production Server**
   ```bash
   npm run start
   ```

### Docker Deployment

```bash
# Build Docker image
docker build -t omnilyzer .

# Run container
docker run -p 3000:3000 -p 3001:3001 omnilyzer
```

### Environment-Specific Configurations

- **Development**: `http://localhost:3000`
- **Staging**: `https://dev.omnilyzer.ai`
- **Production**: `https://omnilyzer.ai`

## 📊 Analytics Features

### Real-Time Tracking
- Page view tracking with session management
- User behavior analytics (clicks, scrolls, time on page)
- Conversion funnel analysis
- Real-time visitor monitoring

### Business Intelligence
- Custom dashboard with KPI metrics
- User segmentation and cohort analysis
- Performance monitoring and alerts
- Export capabilities for data analysis

### Admin Tools
- User management and authentication
- Content management system
- System health monitoring
- Security audit logs

## 🛡️ Security

- **Authentication**: NextAuth.js with Google OAuth
- **Authorization**: Role-based access control
- **Data Protection**: Encrypted data storage
- **Security Headers**: Comprehensive security middleware
- **Rate Limiting**: API rate limiting and DDoS protection

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/MichaelTrueX/omnilyzer/issues)
- **Email**: <EMAIL>

## 🎯 Roadmap

- [ ] Advanced AI-powered analytics
- [ ] Multi-tenant architecture
- [ ] Mobile application
- [ ] Advanced reporting tools
- [ ] Third-party integrations

---

**Built with ❤️ by the Omnilyzer Team**

*Transform your business data into actionable insights with Omnilyzer's powerful analytics platform.*
