'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../lib/auth-context';

interface TableInfo {
  name: string;
  description: string;
  fields: string[];
}

interface TableData {
  [key: string]: any;
}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

export default function DatabaseManagement() {
  const { user, hasRole } = useAuth();
  const [tables, setTables] = useState<Record<string, TableInfo>>({});
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [tableData, setTableData] = useState<TableData[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [editingRecord, setEditingRecord] = useState<TableData | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);

  useEffect(() => {
    if (hasRole('Admin')) {
      fetchTables();
    }
  }, [hasRole]);

  useEffect(() => {
    if (selectedTable) {
      fetchTableData(selectedTable, 1);
    }
  }, [selectedTable]);

  const fetchTables = async () => {
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/v1/admin/database/tables', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTables(data.tables);
        if (Object.keys(data.tables).length > 0) {
          setSelectedTable(Object.keys(data.tables)[0]);
        }
      } else {
        setError('Failed to load database tables');
      }
    } catch (error) {
      console.error('Error fetching tables:', error);
      setError('Network error occurred');
    }
  };

  const fetchTableData = async (table: string, page: number = 1) => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/v1/admin/database/${table}?page=${page}&limit=20`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTableData(data.data);
        setPagination(data.pagination);
      } else {
        setError('Failed to load table data');
      }
    } catch (error) {
      console.error('Error fetching table data:', error);
      setError('Network error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (record: TableData) => {
    setEditingRecord({ ...record });
    setShowEditModal(true);
  };

  const handleSave = async () => {
    if (!editingRecord) return;

    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/v1/admin/database/${selectedTable}/${editingRecord.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editingRecord),
      });

      if (response.ok) {
        setShowEditModal(false);
        setEditingRecord(null);
        fetchTableData(selectedTable, pagination.page);
      } else {
        setError('Failed to update record');
      }
    } catch (error) {
      console.error('Error updating record:', error);
      setError('Network error occurred');
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this record?')) return;

    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/v1/admin/database/${selectedTable}/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        fetchTableData(selectedTable, pagination.page);
      } else {
        setError('Failed to delete record');
      }
    } catch (error) {
      console.error('Error deleting record:', error);
      setError('Network error occurred');
    }
  };

  const formatValue = (value: any): string => {
    if (value === null || value === undefined) return 'NULL';
    if (typeof value === 'boolean') return value ? 'true' : 'false';
    if (typeof value === 'object') return JSON.stringify(value);
    if (typeof value === 'string' && value.length > 50) {
      return value.substring(0, 50) + '...';
    }
    return String(value);
  };

  if (!hasRole('Admin')) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Access denied. Admin privileges required.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="pb-5 border-b border-gray-200 mb-6">
        <h1 className="text-3xl font-bold leading-tight text-gray-900">Database Management</h1>
        <p className="mt-2 text-sm text-gray-600">
          View and edit database tables and records
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <p className="text-red-800">{error}</p>
          <button
            onClick={() => setError(null)}
            className="mt-2 text-red-600 hover:text-red-800 text-sm"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Table Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Table
        </label>
        <select
          value={selectedTable}
          onChange={(e) => setSelectedTable(e.target.value)}
          className="block w-full max-w-md px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          {tables && Object.entries(tables).map(([key, table]) => (
            <option key={key} value={key}>
              {table.name} - {table.description}
            </option>
          ))}
        </select>
      </div>

      {/* Table Data */}
      {selectedTable && (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              {tables[selectedTable]?.name}
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              {tables[selectedTable]?.description} ({pagination.total} records)
            </p>
          </div>

          {isLoading ? (
            <div className="text-center py-8">
              <p className="text-gray-500">Loading...</p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      {tables[selectedTable]?.fields?.map((field) => (
                        <th
                          key={field}
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {field}
                        </th>
                      ))}
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {tableData.map((record) => (
                      <tr key={record.id} className="hover:bg-gray-50">
                        {tables[selectedTable]?.fields.map((field) => (
                          <td key={field} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatValue(record[field])}
                          </td>
                        ))}
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => handleEdit(record)}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDelete(record.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {pagination.pages > 1 && (
                <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                  <div className="flex-1 flex justify-between sm:hidden">
                    <button
                      onClick={() => fetchTableData(selectedTable, pagination.page - 1)}
                      disabled={pagination.page === 1}
                      className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => fetchTableData(selectedTable, pagination.page + 1)}
                      disabled={pagination.page === pagination.pages}
                      className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      Next
                    </button>
                  </div>
                  <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-gray-700">
                        Showing page {pagination.page} of {pagination.pages} ({pagination.total} total records)
                      </p>
                    </div>
                    <div>
                      <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        {Array.from({ length: Math.min(pagination.pages, 5) }, (_, i) => {
                          const page = i + 1;
                          return (
                            <button
                              key={page}
                              onClick={() => fetchTableData(selectedTable, page)}
                              className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${page === pagination.page
                                ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                }`}
                            >
                              {page}
                            </button>
                          );
                        })}
                      </nav>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      )}

      {/* Edit Modal */}
      {showEditModal && editingRecord && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Edit {tables[selectedTable]?.name} Record
              </h3>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {tables[selectedTable]?.fields.map((field) => (
                  <div key={field}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {field}
                    </label>
                    {field === 'id' ? (
                      <input
                        type="text"
                        value={editingRecord[field] || ''}
                        disabled
                        className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500"
                      />
                    ) : field.includes('password') ? (
                      <input
                        type="password"
                        value=""
                        disabled
                        placeholder="Password fields cannot be edited here"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500"
                      />
                    ) : field.includes('At') || field.includes('Date') ? (
                      <input
                        type="datetime-local"
                        value={editingRecord[field] ? new Date(editingRecord[field]).toISOString().slice(0, 16) : ''}
                        onChange={(e) => setEditingRecord({
                          ...editingRecord,
                          [field]: e.target.value ? new Date(e.target.value).toISOString() : null
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                    ) : typeof editingRecord[field] === 'boolean' ? (
                      <select
                        value={editingRecord[field] ? 'true' : 'false'}
                        onChange={(e) => setEditingRecord({
                          ...editingRecord,
                          [field]: e.target.value === 'true'
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="true">True</option>
                        <option value="false">False</option>
                      </select>
                    ) : field === 'status' && selectedTable === 'contactSubmissions' ? (
                      <select
                        value={editingRecord[field] || ''}
                        onChange={(e) => setEditingRecord({
                          ...editingRecord,
                          [field]: e.target.value
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="new">New</option>
                        <option value="contacted">Contacted</option>
                        <option value="converted">Converted</option>
                        <option value="closed">Closed</option>
                      </select>
                    ) : field === 'role' && selectedTable === 'users' ? (
                      <select
                        value={editingRecord[field] || ''}
                        onChange={(e) => setEditingRecord({
                          ...editingRecord,
                          [field]: e.target.value
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="User">User</option>
                        <option value="Admin">Admin</option>
                        <option value="Developer">Developer</option>
                      </select>
                    ) : (
                      <textarea
                        value={editingRecord[field] || ''}
                        onChange={(e) => setEditingRecord({
                          ...editingRecord,
                          [field]: e.target.value
                        })}
                        rows={field === 'message' ? 4 : 1}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                    )}
                  </div>
                ))}
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditingRecord(null);
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
