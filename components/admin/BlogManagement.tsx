'use client';

import { useState, useEffect } from 'react';

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  summary: string;
  content: string;
  author: string;
  status: 'draft' | 'published' | 'archived';
  category?: string;
  tags?: string[];
  readTime?: number;
  trendTopic?: string;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export default function BlogManagement() {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPost, setSelectedPost] = useState<BlogPost | null>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [filter, setFilter] = useState('all');
  const [showSocialModal, setShowSocialModal] = useState(false);
  const [socialPosts, setSocialPosts] = useState<any[]>([]);
  const [publishing, setPublishing] = useState(false);

  useEffect(() => {
    fetchPosts();
  }, [filter]);

  const fetchPosts = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/v1/blog?status=${filter}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPosts(data.data.posts);
      } else {
        setError('Failed to load blog posts');
      }
    } catch (error) {
      console.error('Error fetching posts:', error);
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const generateBlogPost = async () => {
    try {
      setGenerating(true);
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/v1/blog/generate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          trendTopic: 'AI automation for small businesses'
        }),
      });

      if (response.ok) {
        const data = await response.json();
        await fetchPosts(); // Refresh the list
        alert('Blog post generated successfully!');
      } else {
        setError('Failed to generate blog post');
      }
    } catch (error) {
      setError('Error generating blog post');
    } finally {
      setGenerating(false);
    }
  };

  const updatePostStatus = async (postId: number, status: string) => {
    try {
      const token = localStorage.getItem('accessToken');
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      const response = await fetch(`/api/v1/blog/${post.slug}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (response.ok) {
        await fetchPosts();
      } else {
        setError('Failed to update post status');
      }
    } catch (error) {
      setError('Error updating post status');
    }
  };

  const updatePost = async (updatedPost: BlogPost) => {
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/v1/blog/${updatedPost.slug}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: updatedPost.title,
          summary: updatedPost.summary,
          content: updatedPost.content,
          category: updatedPost.category,
          tags: updatedPost.tags,
          status: updatedPost.status
        }),
      });

      if (response.ok) {
        await fetchPosts();
        setShowEditor(false);
        setSelectedPost(null);
      } else {
        setError('Failed to update post');
      }
    } catch (error) {
      setError('Error updating post');
    }
  };

  const fetchSocialPosts = async (blogPostId: number) => {
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/v1/blog/social?blogPostId=${blogPostId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSocialPosts(data.data);
      }
    } catch (error) {
      console.error('Error fetching social posts:', error);
    }
  };

  const publishToSocial = async (platforms: string[]) => {
    if (!selectedPost) return;

    try {
      setPublishing(true);
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/v1/blog/social/publish', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          blogPostId: selectedPost.id,
          platforms
        }),
      });

      if (response.ok) {
        const data = await response.json();
        await fetchSocialPosts(selectedPost.id);
        alert(`Published to ${platforms.join(' and ')} successfully!`);
      } else {
        setError('Failed to publish to social media');
      }
    } catch (error) {
      setError('Error publishing to social media');
    } finally {
      setPublishing(false);
    }
  };

  const deletePost = async (postId: number) => {
    if (!confirm('Are you sure you want to delete this blog post?')) return;

    try {
      const token = localStorage.getItem('accessToken');
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      const response = await fetch(`/api/v1/blog/${post.slug}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        await fetchPosts();
      } else {
        setError('Failed to delete post');
      }
    } catch (error) {
      setError('Error deleting post');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'archived': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Blog Management</h2>
          <p className="text-gray-600">Manage AI-generated blog posts and content</p>
        </div>
        <button
          onClick={generateBlogPost}
          disabled={generating}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          {generating ? 'Generating...' : 'Generate New Post'}
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
          <button
            onClick={() => setError(null)}
            className="mt-2 text-red-600 hover:text-red-800 underline"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Filters */}
      <div className="flex space-x-2">
        {['all', 'draft', 'published', 'archived'].map((status) => (
          <button
            key={status}
            onClick={() => setFilter(status)}
            className={`px-4 py-2 rounded-lg text-sm font-medium ${filter === status
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </button>
        ))}
      </div>

      {/* Posts Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Title
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Published
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {posts.map((post) => (
                <tr key={post.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{post.title}</div>
                      <div className="text-sm text-gray-500">
                        {post.readTime} min read • {post.trendTopic}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(post.status)}`}>
                      {post.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {post.category || 'Uncategorized'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {post.publishedAt ? new Date(post.publishedAt).toLocaleDateString() : 'Not published'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button
                      onClick={() => {
                        setSelectedPost(post);
                        setShowEditor(true);
                      }}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      Edit
                    </button>
                    {post.status === 'published' && (
                      <button
                        onClick={() => {
                          setSelectedPost(post);
                          fetchSocialPosts(post.id);
                          setShowSocialModal(true);
                        }}
                        className="text-purple-600 hover:text-purple-900"
                      >
                        Social
                      </button>
                    )}
                    {post.status === 'draft' && (
                      <button
                        onClick={() => updatePostStatus(post.id, 'published')}
                        className="text-green-600 hover:text-green-900"
                      >
                        Publish
                      </button>
                    )}
                    {post.status === 'published' && (
                      <button
                        onClick={() => updatePostStatus(post.id, 'archived')}
                        className="text-yellow-600 hover:text-yellow-900"
                      >
                        Archive
                      </button>
                    )}
                    <button
                      onClick={() => deletePost(post.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                    {post.status === 'published' && (
                      <a
                        href={`/blog/${post.slug}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-purple-600 hover:text-purple-900"
                      >
                        View
                      </a>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {posts.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg">No blog posts found</div>
          <button
            onClick={generateBlogPost}
            className="mt-4 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Generate Your First Post
          </button>
        </div>
      )}

      {/* Edit Modal */}
      {showEditor && selectedPost && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-gray-900">Edit Blog Post</h3>
              <button
                onClick={() => {
                  setShowEditor(false);
                  setSelectedPost(null);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Title</label>
                <input
                  type="text"
                  value={selectedPost.title}
                  onChange={(e) => setSelectedPost({ ...selectedPost, title: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Summary</label>
                <textarea
                  value={selectedPost.summary}
                  onChange={(e) => setSelectedPost({ ...selectedPost, summary: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <input
                    type="text"
                    value={selectedPost.category || ''}
                    onChange={(e) => setSelectedPost({ ...selectedPost, category: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select
                    value={selectedPost.status}
                    onChange={(e) => setSelectedPost({ ...selectedPost, status: e.target.value as 'draft' | 'published' | 'archived' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="draft">Draft</option>
                    <option value="published">Published</option>
                    <option value="archived">Archived</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tags (comma-separated)</label>
                <input
                  type="text"
                  value={selectedPost.tags?.join(', ') || ''}
                  onChange={(e) => setSelectedPost({
                    ...selectedPost,
                    tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="AI, Automation, SMB"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Content</label>
                <textarea
                  value={selectedPost.content}
                  onChange={(e) => setSelectedPost({ ...selectedPost, content: e.target.value })}
                  rows={15}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                  placeholder="Write your blog content here..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={() => {
                  setShowEditor(false);
                  setSelectedPost(null);
                }}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={() => updatePost(selectedPost)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Social Media Modal */}
      {showSocialModal && selectedPost && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-gray-900">Social Media Publishing</h3>
              <button
                onClick={() => {
                  setShowSocialModal(false);
                  setSelectedPost(null);
                  setSocialPosts([]);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="mb-6">
              <h4 className="text-md font-semibold text-gray-800 mb-2">{selectedPost.title}</h4>
              <p className="text-sm text-gray-600">{selectedPost.summary}</p>
            </div>

            {/* Publishing Actions */}
            <div className="mb-6">
              <h5 className="text-sm font-medium text-gray-700 mb-3">Publish to:</h5>
              <div className="flex space-x-4">
                <button
                  onClick={() => publishToSocial(['facebook'])}
                  disabled={publishing}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                  </svg>
                  {publishing ? 'Publishing...' : 'Facebook'}
                </button>
                <button
                  onClick={() => publishToSocial(['linkedin'])}
                  disabled={publishing}
                  className="flex items-center px-4 py-2 bg-blue-800 text-white rounded-lg hover:bg-blue-900 disabled:opacity-50"
                >
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                  </svg>
                  {publishing ? 'Publishing...' : 'LinkedIn'}
                </button>
                <button
                  onClick={() => publishToSocial(['facebook', 'linkedin'])}
                  disabled={publishing}
                  className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
                >
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                  </svg>
                  {publishing ? 'Publishing...' : 'Both Platforms'}
                </button>
              </div>
            </div>

            {/* Social Posts History */}
            <div>
              <h5 className="text-sm font-medium text-gray-700 mb-3">Publishing History:</h5>
              {socialPosts.length === 0 ? (
                <p className="text-sm text-gray-500">No social media posts yet.</p>
              ) : (
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {socialPosts.map((socialPost) => (
                    <div key={socialPost.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div className="flex items-center">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-2 ${socialPost.platform === 'facebook' ? 'bg-blue-100 text-blue-800' : 'bg-blue-100 text-blue-900'
                          }`}>
                          {socialPost.platform}
                        </span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${socialPost.status === 'posted' ? 'bg-green-100 text-green-800' :
                            socialPost.status === 'failed' ? 'bg-red-100 text-red-800' :
                              'bg-yellow-100 text-yellow-800'
                          }`}>
                          {socialPost.status}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500">
                        {socialPost.postedAt ? new Date(socialPost.postedAt).toLocaleDateString() :
                          new Date(socialPost.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={() => {
                  setShowSocialModal(false);
                  setSelectedPost(null);
                  setSocialPosts([]);
                }}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
