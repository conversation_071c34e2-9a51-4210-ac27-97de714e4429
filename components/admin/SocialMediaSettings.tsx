'use client';

import { useState, useEffect } from 'react';

interface SocialSettings {
  autoPublishEnabled: boolean;
  platforms: {
    facebook: boolean;
    linkedin: boolean;
  };
  defaultMessage: string;
}

export default function SocialMediaSettings() {
  const [settings, setSettings] = useState<SocialSettings>({
    autoPublishEnabled: true,
    platforms: {
      facebook: true,
      linkedin: true
    },
    defaultMessage: 'Check out our latest blog post!'
  });
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = () => {
    // Load from localStorage for demo purposes
    const saved = localStorage.getItem('socialMediaSettings');
    if (saved) {
      try {
        setSettings(JSON.parse(saved));
      } catch (error) {
        console.error('Error loading social media settings:', error);
      }
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      // Save to localStorage for demo purposes
      localStorage.setItem('socialMediaSettings', JSON.stringify(settings));
      setMessage('Settings saved successfully!');
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      setMessage('Error saving settings');
      setTimeout(() => setMessage(''), 3000);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Social Media Publishing Settings</h3>
        <p className="text-sm text-gray-600">Configure automatic social media publishing for blog posts.</p>
      </div>

      {message && (
        <div className={`mb-4 p-3 rounded-md ${
          message.includes('Error') ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'
        }`}>
          {message}
        </div>
      )}

      <div className="space-y-6">
        {/* Auto-publish toggle */}
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Automatic Publishing</h4>
            <p className="text-sm text-gray-500">Automatically publish to social media when blog posts are published</p>
          </div>
          <button
            type="button"
            onClick={() => setSettings(prev => ({ ...prev, autoPublishEnabled: !prev.autoPublishEnabled }))}
            className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 ${
              settings.autoPublishEnabled ? 'bg-blue-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                settings.autoPublishEnabled ? 'translate-x-5' : 'translate-x-0'
              }`}
            />
          </button>
        </div>

        {/* Platform selection */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Publishing Platforms</h4>
          <div className="space-y-3">
            <div className="flex items-center">
              <input
                id="facebook"
                type="checkbox"
                checked={settings.platforms.facebook}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  platforms: { ...prev.platforms, facebook: e.target.checked }
                }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="facebook" className="ml-3 flex items-center">
                <svg className="w-5 h-5 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                <span className="text-sm text-gray-700">Facebook Business Page</span>
              </label>
            </div>
            <div className="flex items-center">
              <input
                id="linkedin"
                type="checkbox"
                checked={settings.platforms.linkedin}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  platforms: { ...prev.platforms, linkedin: e.target.checked }
                }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="linkedin" className="ml-3 flex items-center">
                <svg className="w-5 h-5 mr-2 text-blue-800" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
                <span className="text-sm text-gray-700">LinkedIn Company Page</span>
              </label>
            </div>
          </div>
        </div>

        {/* API Status */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">API Status</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-md">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
                <span className="text-sm text-yellow-800">Facebook API</span>
              </div>
              <span className="text-xs text-yellow-600">Mock Mode (90% success rate)</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-md">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
                <span className="text-sm text-yellow-800">LinkedIn API</span>
              </div>
              <span className="text-xs text-yellow-600">Mock Mode (85% success rate)</span>
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            To enable real API integration, see <code>/docs/SOCIAL_MEDIA_SETUP.md</code>
          </p>
        </div>

        {/* Current URLs */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Social Media Accounts</h4>
          <div className="space-y-2">
            <div className="flex items-center text-sm text-gray-600">
              <svg className="w-4 h-4 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
              <a href="https://www.facebook.com/profile.php?id=**************" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                facebook.com/profile.php?id=**************
              </a>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <svg className="w-4 h-4 mr-2 text-blue-800" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
              <a href="https://www.linkedin.com/company/omnilyzer/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                linkedin.com/company/omnilyzer/
              </a>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 flex justify-end">
        <button
          onClick={saveSettings}
          disabled={saving}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          {saving ? 'Saving...' : 'Save Settings'}
        </button>
      </div>
    </div>
  );
}
