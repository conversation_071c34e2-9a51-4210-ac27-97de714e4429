'use client';

import { useState, useEffect, useRef } from 'react';
import { <PERSON>, Doughnut, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface SystemMetrics {
  system: {
    cpu: { usage: number; cores: number; temperature?: number };
    memory: { total: number; used: number; free: number; usage: number };
    disk: Array<{ filesystem: string; size: number; used: number; usage: number }>;
    uptime: { formatted: string };
  };
  application: {
    apiHealth: { status: string; responseTime: number };
    processes: Array<{ pid: number; name: string; cpu: number; memory: number }>;
    activeConnections: number;
  };
  database: {
    status: { status: string };
    connections: { active: number; max: number; usage: number };
    size: { sizeInMB: number };
  };
  ssl: {
    certificate: { daysUntilExpiry: number; status: string };
  };
  alerts: Array<{
    type: string;
    category: string;
    message: string;
    timestamp: string;
  }>;
  history: {
    cpu: Array<{ timestamp: string; value: number }>;
    memory: Array<{ timestamp: string; value: number }>;
    disk: Array<{ timestamp: string; value: number }>;
  };
}

export default function HealthDashboard() {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');
  const wsRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    connectWebSocket();
    fetchInitialMetrics();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  const connectWebSocket = () => {
    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.hostname}:3001/ws/metrics`;

      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        setConnectionStatus('connected');
        console.log('WebSocket connected for health metrics');

        // Subscribe to all metrics
        wsRef.current?.send(JSON.stringify({
          type: 'subscribe',
          categories: ['all']
        }));
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          switch (data.type) {
            case 'initial_metrics':
            case 'metrics_update':
              setMetrics(data.data);
              setLoading(false);
              break;
            case 'alert':
              // Handle real-time alerts
              console.log('New alert:', data.data);
              break;
            case 'error':
              console.error('WebSocket error:', data.message);
              break;
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      wsRef.current.onclose = () => {
        setConnectionStatus('disconnected');
        console.log('WebSocket disconnected');

        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          if (wsRef.current?.readyState === WebSocket.CLOSED) {
            connectWebSocket();
          }
        }, 5000);
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionStatus('disconnected');
      };

    } catch (error) {
      console.error('Error connecting WebSocket:', error);
      setConnectionStatus('disconnected');
    }
  };

  const fetchInitialMetrics = async () => {
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/v1/health/dashboard/metrics', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        setMetrics(result.data);
      } else {
        setError('Failed to fetch health metrics');
      }
    } catch (err) {
      setError('Error loading health metrics');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getUsageColor = (usage: number) => {
    if (usage >= 90) return 'bg-red-500';
    if (usage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-800">{error}</p>
        <button
          onClick={fetchInitialMetrics}
          className="mt-2 text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  if (!metrics) {
    return <div>No metrics available</div>;
  }

  // Chart data for CPU/Memory/Disk history
  const systemHistoryData = {
    labels: metrics.history.cpu.slice(-20).map(point =>
      new Date(point.timestamp).toLocaleTimeString()
    ),
    datasets: [
      {
        label: 'CPU %',
        data: metrics.history.cpu.slice(-20).map(point => point.value),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Memory %',
        data: metrics.history.memory.slice(-20).map(point => point.value),
        borderColor: 'rgb(16, 185, 129)',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Disk %',
        data: metrics.history.disk.slice(-20).map(point => point.value),
        borderColor: 'rgb(245, 158, 11)',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        tension: 0.4,
      },
    ],
  };

  return (
    <div className="space-y-6">
      {/* Header with Connection Status */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">System Health Dashboard</h2>
        <div className="flex items-center space-x-4">
          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${connectionStatus === 'connected' ? 'bg-green-100 text-green-800' :
              connectionStatus === 'connecting' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
            }`}>
            <div className={`w-2 h-2 rounded-full ${connectionStatus === 'connected' ? 'bg-green-500' :
                connectionStatus === 'connecting' ? 'bg-yellow-500' :
                  'bg-red-500'
              }`}></div>
            <span>{connectionStatus === 'connected' ? 'Live' : connectionStatus}</span>
          </div>
          <span className="text-sm text-gray-500">
            Last updated: {new Date().toLocaleTimeString()}
          </span>
        </div>
      </div>

      {/* Alerts */}
      {metrics.alerts && metrics.alerts.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-red-800 mb-2">Active Alerts</h3>
          <div className="space-y-2">
            {metrics.alerts.map((alert, index) => (
              <div key={index} className="flex items-center space-x-2">
                <span className={`px-2 py-1 rounded text-xs font-medium ${alert.type === 'critical' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                  {alert.type}
                </span>
                <span className="text-sm">{alert.message}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* System Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* CPU */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">CPU</h3>
            <span className="text-2xl font-bold text-gray-900">{metrics.system.cpu.usage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
            <div
              className={`h-2 rounded-full ${getUsageColor(metrics.system.cpu.usage)}`}
              style={{ width: `${metrics.system.cpu.usage}%` }}
            ></div>
          </div>
          <div className="text-sm text-gray-600">
            {metrics.system.cpu.cores} cores
            {metrics.system.cpu.temperature && ` • ${metrics.system.cpu.temperature}°C`}
          </div>
        </div>

        {/* Memory */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Memory</h3>
            <span className="text-2xl font-bold text-gray-900">{metrics.system.memory.usage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
            <div
              className={`h-2 rounded-full ${getUsageColor(metrics.system.memory.usage)}`}
              style={{ width: `${metrics.system.memory.usage}%` }}
            ></div>
          </div>
          <div className="text-sm text-gray-600">
            {metrics.system.memory.used.toFixed(1)}GB / {metrics.system.memory.total.toFixed(1)}GB
          </div>
        </div>

        {/* Disk */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Disk</h3>
            <span className="text-2xl font-bold text-gray-900">
              {metrics.system.disk[0]?.usage || 0}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
            <div
              className={`h-2 rounded-full ${getUsageColor(metrics.system.disk[0]?.usage || 0)}`}
              style={{ width: `${metrics.system.disk[0]?.usage || 0}%` }}
            ></div>
          </div>
          <div className="text-sm text-gray-600">
            {metrics.system.disk[0]?.used.toFixed(1)}GB / {metrics.system.disk[0]?.size.toFixed(1)}GB
          </div>
        </div>

        {/* Uptime */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Uptime</h3>
            <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="text-lg font-semibold text-gray-900 mb-2">
            {metrics.system.uptime.formatted}
          </div>
          <div className="text-sm text-gray-600">System uptime</div>
        </div>
      </div>

      {/* Services Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* API Health */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">API</h3>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(metrics.application.apiHealth.status)}`}>
              {metrics.application.apiHealth.status}
            </span>
          </div>
          <div className="text-sm text-gray-600">
            Response time: {metrics.application.apiHealth.responseTime}ms
          </div>
          <div className="text-sm text-gray-600">
            Active connections: {metrics.application.activeConnections}
          </div>
        </div>

        {/* Database */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Database</h3>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(metrics.database.status.status)}`}>
              {metrics.database.status.status}
            </span>
          </div>
          <div className="text-sm text-gray-600">
            Connections: {metrics.database.connections.active}/{metrics.database.connections.max}
          </div>
          <div className="text-sm text-gray-600">
            Size: {metrics.database.size.sizeInMB.toFixed(1)}MB
          </div>
        </div>

        {/* SSL Certificate */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">SSL</h3>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(metrics.ssl.certificate.status)}`}>
              {metrics.ssl.certificate.status}
            </span>
          </div>
          <div className="text-sm text-gray-600">
            Expires in: {metrics.ssl.certificate.daysUntilExpiry} days
          </div>
        </div>

        {/* Processes */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Processes</h3>
            <span className="text-2xl font-bold text-gray-900">{metrics.application.processes.length}</span>
          </div>
          <div className="text-sm text-gray-600">
            Active Node.js processes
          </div>
        </div>
      </div>

      {/* System Performance Chart */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">System Performance (Last 20 minutes)</h3>
        <Line data={systemHistoryData} options={{
          responsive: true,
          plugins: {
            legend: {
              position: 'top' as const,
            },
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              ticks: {
                callback: function (value) {
                  return value + '%';
                }
              }
            },
          },
        }} />
      </div>

      {/* Process Details */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Running Processes</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PID</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CPU %</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Memory (MB)</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {metrics.application.processes.slice(0, 10).map((process, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{process.pid}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{process.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{process.cpu.toFixed(1)}%</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{process.memory.toFixed(1)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
