'use client';

import { useState, useEffect } from 'react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    loadAverage: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
    percentage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    percentage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
  };
  services: {
    frontend: { status: 'healthy' | 'warning' | 'error'; uptime: number; responseTime: number };
    backend: { status: 'healthy' | 'warning' | 'error'; uptime: number; responseTime: number };
    database: { status: 'healthy' | 'warning' | 'error'; connections: number; queryTime: number };
    nginx: { status: 'healthy' | 'warning' | 'error'; activeConnections: number };
  };
  alerts: Array<{
    id: string;
    type: 'error' | 'warning' | 'info';
    message: string;
    timestamp: string;
    resolved: boolean;
  }>;
  uptime: number;
  lastUpdated: string;
}

export default function SystemHealthDashboard() {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  useEffect(() => {
    fetchSystemMetrics();

    if (autoRefresh) {
      const interval = setInterval(fetchSystemMetrics, refreshInterval * 1000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const fetchSystemMetrics = async () => {
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/v1/admin/system-health', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setMetrics(data.data);
      } else {
        // Mock data for development
        setMetrics({
          cpu: {
            usage: Math.random() * 80 + 10,
            cores: 4,
            loadAverage: [1.2, 1.5, 1.8]
          },
          memory: {
            total: 2048,
            used: Math.random() * 1500 + 300,
            free: 0,
            percentage: 0
          },
          disk: {
            total: 50000,
            used: Math.random() * 30000 + 10000,
            free: 0,
            percentage: 0
          },
          network: {
            bytesIn: Math.random() * 1000000,
            bytesOut: Math.random() * 800000,
            packetsIn: Math.random() * 10000,
            packetsOut: Math.random() * 8000
          },
          services: {
            frontend: { status: 'healthy', uptime: 99.8, responseTime: Math.random() * 200 + 50 },
            backend: { status: 'healthy', uptime: 99.9, responseTime: Math.random() * 100 + 20 },
            database: { status: 'healthy', connections: Math.floor(Math.random() * 50) + 10, queryTime: Math.random() * 50 + 5 },
            nginx: { status: 'healthy', activeConnections: Math.floor(Math.random() * 100) + 20 }
          },
          alerts: [
            {
              id: '1',
              type: 'warning',
              message: 'High memory usage detected (85%)',
              timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
              resolved: false
            },
            {
              id: '2',
              type: 'info',
              message: 'System backup completed successfully',
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
              resolved: true
            }
          ],
          uptime: 99.95,
          lastUpdated: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Failed to fetch system metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      default: return '❓';
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (uptime: number): string => {
    return `${uptime.toFixed(2)}%`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Unable to load system metrics</p>
        <button
          onClick={fetchSystemMetrics}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  // Calculate derived values
  metrics.memory.free = metrics.memory.total - metrics.memory.used;
  metrics.memory.percentage = (metrics.memory.used / metrics.memory.total) * 100;
  metrics.disk.free = metrics.disk.total - metrics.disk.used;
  metrics.disk.percentage = (metrics.disk.used / metrics.disk.total) * 100;

  const resourceUsageData = {
    labels: ['CPU', 'Memory', 'Disk'],
    datasets: [
      {
        label: 'Usage %',
        data: [metrics.cpu.usage, metrics.memory.percentage, metrics.disk.percentage],
        backgroundColor: [
          metrics.cpu.usage > 80 ? '#EF4444' : metrics.cpu.usage > 60 ? '#F59E0B' : '#10B981',
          metrics.memory.percentage > 80 ? '#EF4444' : metrics.memory.percentage > 60 ? '#F59E0B' : '#10B981',
          metrics.disk.percentage > 80 ? '#EF4444' : metrics.disk.percentage > 60 ? '#F59E0B' : '#10B981',
        ],
        borderWidth: 0,
      },
    ],
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">System Health Dashboard</h2>
          <p className="text-gray-600">Real-time monitoring of system performance and services</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-600">Auto-refresh:</label>
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded"
            />
          </div>
          <select
            value={refreshInterval}
            onChange={(e) => setRefreshInterval(Number(e.target.value))}
            className="px-3 py-1 border border-gray-300 rounded text-sm"
            disabled={!autoRefresh}
          >
            <option value={10}>10s</option>
            <option value={30}>30s</option>
            <option value={60}>1m</option>
            <option value={300}>5m</option>
          </select>
          <button
            onClick={fetchSystemMetrics}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
          >
            Refresh Now
          </button>
        </div>
      </div>

      {/* System Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">System Uptime</p>
              <p className="text-2xl font-bold text-green-600">{formatUptime(metrics.uptime)}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">CPU Usage</p>
              <p className="text-2xl font-bold text-blue-600">{metrics.cpu.usage.toFixed(1)}%</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Memory Usage</p>
              <p className="text-2xl font-bold text-purple-600">{metrics.memory.percentage.toFixed(1)}%</p>
              <p className="text-xs text-gray-500">{formatBytes(metrics.memory.used)} / {formatBytes(metrics.memory.total)}</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Disk Usage</p>
              <p className="text-2xl font-bold text-orange-600">{metrics.disk.percentage.toFixed(1)}%</p>
              <p className="text-xs text-gray-500">{formatBytes(metrics.disk.used)} / {formatBytes(metrics.disk.total)}</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Services Status */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Services Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Object.entries(metrics.services).map(([service, data]) => (
            <div key={service} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900 capitalize">{service}</h4>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(data.status)}`}>
                  {getStatusIcon(data.status)} {data.status}
                </span>
              </div>
              <div className="space-y-1 text-sm text-gray-600">
                {service !== 'nginx' && (
                  <>
                    <div>Uptime: {formatUptime((data as any).uptime || 0)}</div>
                    <div>Response: {(data as any).responseTime?.toFixed(0) || 0}ms</div>
                  </>
                )}
                {service === 'database' && (
                  <div>Connections: {data.connections}</div>
                )}
                {service === 'nginx' && (
                  <div>Active Connections: {data.activeConnections}</div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Charts and Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Resource Usage Chart */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Resource Usage</h3>
          <div className="h-64">
            <Bar
              data={resourceUsageData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                      callback: function (value) {
                        return value + '%';
                      }
                    }
                  }
                },
                plugins: {
                  legend: {
                    display: false,
                  },
                },
              }}
            />
          </div>
        </div>

        {/* Recent Alerts */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Alerts</h3>
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {metrics.alerts.map((alert) => (
              <div key={alert.id} className={`p-3 rounded-lg border-l-4 ${alert.type === 'error' ? 'border-red-500 bg-red-50' :
                  alert.type === 'warning' ? 'border-yellow-500 bg-yellow-50' :
                    'border-blue-500 bg-blue-50'
                }`}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{alert.message}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {new Date(alert.timestamp).toLocaleString()}
                    </p>
                  </div>
                  {alert.resolved && (
                    <span className="text-green-600 text-xs font-medium">Resolved</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Last Updated */}
      <div className="text-center text-sm text-gray-500">
        Last updated: {new Date(metrics.lastUpdated).toLocaleString()}
      </div>
    </div>
  );
}
