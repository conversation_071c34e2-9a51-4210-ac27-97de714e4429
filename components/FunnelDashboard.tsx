'use client';

import { useState } from 'react';

export default function FunnelDashboard() {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Conversion Funnels</h2>
        <p className="text-gray-600 mb-6">
          Track user journey through your conversion funnels and identify drop-off points.
        </p>
        
        <div className="bg-gray-50 rounded-lg p-8 text-center">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Funnel Dashboard Coming Soon</h3>
          <p className="text-gray-600">
            This feature is currently under development. Track conversion funnels, analyze drop-off rates, and optimize your user journey.
          </p>
        </div>
      </div>
    </div>
  );
}
