'use client';

import { useState, useEffect } from 'react';
import { Bar, Doughnut, Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface SecurityMetrics {
  metrics: {
    totalRequests: number;
    blockedRequests: number;
    rateLimitViolations: number;
    authenticationFailures: number;
    sqlInjectionAttempts: number;
    xssAttempts: number;
    csrfAttempts: number;
    topViolatingIPs: Array<{
      ip: string;
      violations: number;
    }>;
    vulnerabilitySummary: {
      critical: number;
      high: number;
      medium: number;
      low: number;
    };
  };
  recentEvents: Array<{
    type: string;
    ip: string;
    url: string;
    timestamp: string;
    violations?: string[];
  }>;
  scan: {
    timestamp: string;
    checks: {
      headers: { status: string; details: string };
      dependencies: { status: string; details: string };
      configuration: { status: string; details: string };
      permissions: { status: string; details: string };
    };
    recommendations: Array<{
      category: string;
      severity: string;
      description: string;
      action: string;
    }>;
  };
}

export default function SecurityDashboard() {
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(60);

  useEffect(() => {
    fetchSecurityMetrics();

    if (autoRefresh) {
      const interval = setInterval(fetchSecurityMetrics, refreshInterval * 1000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const fetchSecurityMetrics = async () => {
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/v1/admin/security-audit', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setMetrics(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch security metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100 border-red-500';
      case 'high': return 'text-orange-600 bg-orange-100 border-orange-500';
      case 'medium': return 'text-yellow-600 bg-yellow-100 border-yellow-500';
      case 'low': return 'text-blue-600 bg-blue-100 border-blue-500';
      default: return 'text-gray-600 bg-gray-100 border-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return '✅';
      case 'warning': return '⚠️';
      case 'fail': return '❌';
      default: return '❓';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!metrics || !metrics.metrics || !metrics.scan || !metrics.scan.checks) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Unable to load security metrics</p>
        <button
          onClick={fetchSecurityMetrics}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  // Prepare chart data
  const threatData = {
    labels: ['SQL Injection', 'XSS', 'CSRF', 'Rate Limit', 'Auth Failures'],
    datasets: [
      {
        label: 'Security Threats',
        data: [
          metrics.metrics.sqlInjectionAttempts,
          metrics.metrics.xssAttempts,
          metrics.metrics.csrfAttempts,
          metrics.metrics.rateLimitViolations,
          metrics.metrics.authenticationFailures
        ],
        backgroundColor: [
          '#EF4444',
          '#F97316',
          '#EAB308',
          '#3B82F6',
          '#8B5CF6'
        ],
        borderWidth: 0,
      },
    ],
  };

  const vulnerabilityData = {
    labels: ['Critical', 'High', 'Medium', 'Low'],
    datasets: [
      {
        data: [
          metrics.metrics.vulnerabilitySummary.critical,
          metrics.metrics.vulnerabilitySummary.high,
          metrics.metrics.vulnerabilitySummary.medium,
          metrics.metrics.vulnerabilitySummary.low
        ],
        backgroundColor: ['#DC2626', '#EA580C', '#D97706', '#059669'],
        borderWidth: 0,
      },
    ],
  };

  const requestsData = {
    labels: ['Allowed', 'Blocked'],
    datasets: [
      {
        data: [
          metrics.metrics.totalRequests - metrics.metrics.blockedRequests,
          metrics.metrics.blockedRequests
        ],
        backgroundColor: ['#10B981', '#EF4444'],
        borderWidth: 0,
      },
    ],
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Security Dashboard</h2>
          <p className="text-gray-600">Real-time security monitoring and threat detection</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-600">Auto-refresh:</label>
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded"
            />
          </div>
          <select
            value={refreshInterval}
            onChange={(e) => setRefreshInterval(Number(e.target.value))}
            className="px-3 py-1 border border-gray-300 rounded text-sm"
            disabled={!autoRefresh}
          >
            <option value={30}>30s</option>
            <option value={60}>1m</option>
            <option value={300}>5m</option>
          </select>
          <button
            onClick={fetchSecurityMetrics}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Security Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Requests</p>
              <p className="text-2xl font-bold text-blue-600">{metrics.metrics.totalRequests.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Blocked Requests</p>
              <p className="text-2xl font-bold text-red-600">{metrics.metrics.blockedRequests}</p>
            </div>
            <div className="p-3 bg-red-100 rounded-lg">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Auth Failures</p>
              <p className="text-2xl font-bold text-orange-600">{metrics.metrics.authenticationFailures}</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Attack Attempts</p>
              <p className="text-2xl font-bold text-purple-600">
                {metrics.metrics.sqlInjectionAttempts + metrics.metrics.xssAttempts + metrics.metrics.csrfAttempts}
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Threat Types */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Threat Types</h3>
          <div className="h-64">
            <Bar
              data={threatData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    beginAtZero: true,
                  }
                },
                plugins: {
                  legend: {
                    display: false,
                  },
                },
              }}
            />
          </div>
        </div>

        {/* Request Status */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Request Status</h3>
          <div className="h-64">
            <Doughnut
              data={requestsData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'bottom',
                  },
                },
              }}
            />
          </div>
        </div>

        {/* Vulnerability Levels */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Vulnerability Levels</h3>
          <div className="h-64">
            <Doughnut
              data={vulnerabilityData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'bottom',
                  },
                },
              }}
            />
          </div>
        </div>
      </div>

      {/* Security Checks and Recent Events */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Security Checks */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Security Checks</h3>
          <div className="space-y-3">
            {Object.entries(metrics.scan.checks).map(([check, result]) => (
              <div key={check} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getStatusIcon(result.status)}</span>
                  <div>
                    <p className="font-medium text-gray-900 capitalize">{check.replace('_', ' ')}</p>
                    <p className="text-sm text-gray-500">{result.details}</p>
                  </div>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${result.status === 'pass' ? 'bg-green-100 text-green-800' :
                  result.status === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                  {result.status}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Security Events */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Security Events</h3>
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {metrics.recentEvents && metrics.recentEvents.length > 0 ? (
              metrics.recentEvents.map((event, index) => (
                <div key={index} className="p-3 border border-gray-200 rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{event.type.replace('_', ' ')}</p>
                      <p className="text-xs text-gray-500">IP: {event.ip}</p>
                      <p className="text-xs text-gray-500">URL: {event.url}</p>
                      {event.violations && (
                        <p className="text-xs text-red-600">Violations: {event.violations.join(', ')}</p>
                      )}
                    </div>
                    <span className="text-xs text-gray-500">
                      {new Date(event.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500 text-center py-8">No recent security events</p>
            )}
          </div>
        </div>
      </div>

      {/* Top Violating IPs */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Violating IPs</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  IP Address
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Violations
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {metrics.metrics.topViolatingIPs.map((item, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {item.ip}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.violations}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-red-600 hover:text-red-900">Block</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Security Recommendations */}
      {metrics.scan.recommendations && metrics.scan.recommendations.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Security Recommendations</h3>
          <div className="space-y-3">
            {metrics.scan.recommendations.map((rec, index) => (
              <div key={index} className={`p-3 rounded-lg border-l-4 ${getSeverityColor(rec.severity)}`}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{rec.description}</p>
                    <p className="text-xs text-gray-500 mt-1">{rec.action}</p>
                  </div>
                  <span className="text-xs font-medium uppercase">{rec.severity}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
