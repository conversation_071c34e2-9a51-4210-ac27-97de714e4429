'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';

// Generate a session ID that persists for the browser session
function getSessionId(): string {
  if (typeof window === 'undefined') return '';
  
  let sessionId = sessionStorage.getItem('omnilyzer_session_id');
  if (!sessionId) {
    sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    sessionStorage.setItem('omnilyzer_session_id', sessionId);
  }
  return sessionId;
}

// Get or create a user ID that persists across sessions
function getUserId(): string {
  if (typeof window === 'undefined') return '';
  
  let userId = localStorage.getItem('omnilyzer_user_id');
  if (!userId) {
    userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    localStorage.setItem('omnilyzer_user_id', userId);
  }
  return userId;
}

// Track analytics events
async function trackEvent(type: string, path?: string, data?: any) {
  try {
    const sessionId = getSessionId();
    const userId = getUserId();

    await fetch('/api/v1/analytics/track', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type,
        path,
        sessionId,
        userId,
        data
      })
    });
  } catch (error) {
    // Silently fail - don't break the user experience
    console.warn('Analytics tracking failed:', error);
  }
}

// Main analytics tracker component
export default function AnalyticsTracker() {
  const pathname = usePathname();

  useEffect(() => {
    // Track page view when component mounts or pathname changes
    trackEvent('pageview', pathname);
  }, [pathname]);

  useEffect(() => {
    // Track contact form submissions
    const handleContactSubmit = (event: Event) => {
      const form = event.target as HTMLFormElement;
      if (form && form.id === 'contact-form') {
        trackEvent('contact', pathname, {
          formId: form.id,
          action: 'submit'
        });
      }
    };

    // Track button clicks for important actions
    const handleButtonClick = (event: Event) => {
      const button = event.target as HTMLButtonElement;
      if (button && button.dataset.track) {
        trackEvent('custom', pathname, {
          action: 'button_click',
          buttonId: button.id,
          buttonText: button.textContent,
          trackingData: button.dataset.track
        });
      }
    };

    // Track link clicks
    const handleLinkClick = (event: Event) => {
      const link = event.target as HTMLAnchorElement;
      if (link && link.href && link.dataset.track) {
        trackEvent('custom', pathname, {
          action: 'link_click',
          linkHref: link.href,
          linkText: link.textContent,
          trackingData: link.dataset.track
        });
      }
    };

    // Add event listeners
    document.addEventListener('submit', handleContactSubmit);
    document.addEventListener('click', handleButtonClick);
    document.addEventListener('click', handleLinkClick);

    // Cleanup event listeners
    return () => {
      document.removeEventListener('submit', handleContactSubmit);
      document.removeEventListener('click', handleButtonClick);
      document.removeEventListener('click', handleLinkClick);
    };
  }, [pathname]);

  // Track when user leaves the page
  useEffect(() => {
    const handleBeforeUnload = () => {
      trackEvent('custom', pathname, {
        action: 'page_exit',
        timeOnPage: Date.now() - (window as any).pageLoadTime
      });
    };

    // Record page load time
    (window as any).pageLoadTime = Date.now();

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [pathname]);

  // This component doesn't render anything
  return null;
}

// Export the tracking function for manual use
export { trackEvent };
