# Omnilyzer Development Environment Configuration
# Copy this file to .env.development and customize values

# Environment
NODE_ENV=development

# Application URLs
NEXT_PUBLIC_APP_URL=https://dev.omnilyzer.ai
NEXT_PUBLIC_API_URL=https://dev.omnilyzer.ai/api

# Database Configuration
DATABASE_URL=postgresql://localhost:5432/omnilyzer_dev
DATABASE_POOL_SIZE=10

# Analytics Configuration
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
ANALYTICS_ENABLED=true
ANALYTICS_DEBUG=true

# Email Configuration (Development - use Mailtrap or similar)
SMTP_HOST=smtp.mailtrap.io
SMTP_PORT=2525
SMTP_USER=your_mailtrap_user
SMTP_PASS=your_mailtrap_pass
SMTP_FROM=<EMAIL>

# Authentication
JWT_SECRET=your_dev_jwt_secret_here
SESSION_SECRET=your_dev_session_secret_here

# External APIs (Development keys)
GOOGLE_CLIENT_ID=your_dev_google_client_id
GOOGLE_CLIENT_SECRET=your_dev_google_client_secret

# Rate Limiting (Looser for development)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# Logging
LOG_LEVEL=debug
LOG_TO_FILE=true

# Feature Flags
ENABLE_CHATBOT=true
ENABLE_ADVANCED_ANALYTICS=true
ENABLE_HEATMAPS=true

# Development-specific
DISABLE_SSL_VERIFY=true
ENABLE_DEV_TOOLS=true
