#!/bin/bash

# Development Workflow Helper Script
# Ensures proper dual-mode development practices

set -euo pipefail

# Configuration
DEV_DIR="/var/www/omnilyzer"
PROD_DIR="/var/www/omnilyzer-prod"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Function to check current directory
check_current_directory() {
    local current_dir=$(pwd)
    
    if [[ "$current_dir" == "$DEV_DIR"* ]]; then
        echo -e "${GREEN}✅ Working in development directory${NC}"
        return 0
    elif [[ "$current_dir" == "$PROD_DIR"* ]]; then
        echo -e "${RED}❌ WARNING: You are in the production directory!${NC}"
        echo -e "${YELLOW}Please switch to development directory first:${NC}"
        echo -e "${BLUE}cd $DEV_DIR${NC}"
        return 1
    else
        echo -e "${YELLOW}⚠️  You are not in either development or production directory${NC}"
        echo -e "${BLUE}Development: $DEV_DIR${NC}"
        echo -e "${BLUE}Production:  $PROD_DIR${NC}"
        return 1
    fi
}

# Function to show development status
show_dev_status() {
    echo -e "${BLUE}=== Development Environment Status ===${NC}"
    
    # Check if in correct directory
    check_current_directory
    
    # Check services
    echo -e "\n${BLUE}Services Status:${NC}"
    
    # Check port 3000 (Development Frontend)
    if lsof -ti:3000 >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Development Frontend (port 3000): Running${NC}"
    else
        echo -e "${RED}❌ Development Frontend (port 3000): Not running${NC}"
    fi
    
    # Check port 3001 (Backend API)
    if lsof -ti:3001 >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend API (port 3001): Running${NC}"
    else
        echo -e "${RED}❌ Backend API (port 3001): Not running${NC}"
    fi
    
    # Check port 3002 (Production Frontend)
    if lsof -ti:3002 >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Production Frontend (port 3002): Running${NC}"
    else
        echo -e "${RED}❌ Production Frontend (port 3002): Not running${NC}"
    fi
    
    # Check website accessibility
    echo -e "\n${BLUE}Website Accessibility:${NC}"
    
    if curl -s -f "https://dev.omnilyzer.ai" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Development: https://dev.omnilyzer.ai${NC}"
    else
        echo -e "${RED}❌ Development: https://dev.omnilyzer.ai${NC}"
    fi
    
    if curl -s -f "https://omnilyzer.ai" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Production: https://omnilyzer.ai${NC}"
    else
        echo -e "${RED}❌ Production: https://omnilyzer.ai${NC}"
    fi
}

# Function to start development environment
start_dev_environment() {
    echo -e "${BLUE}=== Starting Development Environment ===${NC}"
    
    # Ensure we're in the development directory
    cd "$DEV_DIR"
    
    echo -e "${YELLOW}Starting development services...${NC}"
    
    # Start development frontend
    if ! lsof -ti:3000 >/dev/null 2>&1; then
        echo "Starting development frontend..."
        nohup npm run dev > /var/www/logs/omnilyzer-dev.log 2>&1 &
        echo "Development frontend started"
    else
        echo "Development frontend already running"
    fi
    
    # Start backend API
    if ! lsof -ti:3001 >/dev/null 2>&1; then
        echo "Starting backend API..."
        nohup node server_minimal.js > /var/www/logs/omnilyzer-backend.log 2>&1 &
        echo "Backend API started"
    else
        echo "Backend API already running"
    fi
    
    # Wait for services to start
    echo "Waiting for services to start..."
    sleep 10
    
    # Show status
    show_dev_status
}

# Function to test development environment
test_dev_environment() {
    echo -e "${BLUE}=== Testing Development Environment ===${NC}"
    
    # Test website
    echo "Testing website accessibility..."
    if curl -s -f "https://dev.omnilyzer.ai" >/dev/null; then
        echo -e "${GREEN}✅ Website accessible${NC}"
    else
        echo -e "${RED}❌ Website not accessible${NC}"
        return 1
    fi
    
    # Test chatbot API
    echo "Testing chatbot API..."
    local chatbot_response=$(curl -s -X POST -H "Content-Type: application/json" \
        -d '{"message":"test"}' "https://dev.omnilyzer.ai/api/chatbot" 2>/dev/null)
    
    if echo "$chatbot_response" | grep -q "message"; then
        echo -e "${GREEN}✅ Chatbot API working${NC}"
    else
        echo -e "${RED}❌ Chatbot API not working${NC}"
    fi
    
    # Test frontend logging API
    echo "Testing frontend logging API..."
    local log_response=$(curl -s -X POST -H "Content-Type: application/json" \
        -d '{"logs":[{"level":"info","message":"test"}]}' \
        "https://dev.omnilyzer.ai/api/v1/logs/frontend" 2>/dev/null)
    
    if echo "$log_response" | grep -q "success"; then
        echo -e "${GREEN}✅ Frontend logging API working${NC}"
    else
        echo -e "${RED}❌ Frontend logging API not working${NC}"
    fi
    
    echo -e "${GREEN}Development environment testing completed${NC}"
}

# Function to show deployment readiness
check_deployment_readiness() {
    echo -e "${BLUE}=== Deployment Readiness Check ===${NC}"
    
    # Check if in development directory
    if ! check_current_directory; then
        echo -e "${RED}❌ Not in development directory${NC}"
        return 1
    fi
    
    # Test development environment
    if ! test_dev_environment; then
        echo -e "${RED}❌ Development environment has issues${NC}"
        return 1
    fi
    
    # Check for uncommitted changes (if using git)
    if [ -d ".git" ]; then
        if ! git diff-index --quiet HEAD --; then
            echo -e "${YELLOW}⚠️  You have uncommitted changes${NC}"
            echo "Consider committing your changes before deployment"
        else
            echo -e "${GREEN}✅ No uncommitted changes${NC}"
        fi
    fi
    
    echo -e "${GREEN}✅ Ready for deployment${NC}"
    echo -e "${BLUE}Run: /var/www/deploy_to_production.sh${NC}"
}

# Function to show workflow help
show_help() {
    echo -e "${BLUE}=== Development Workflow Helper ===${NC}"
    echo
    echo "Usage: $0 [command]"
    echo
    echo "Commands:"
    echo "  status    - Show development environment status"
    echo "  start     - Start development environment"
    echo "  test      - Test development environment"
    echo "  ready     - Check if ready for deployment"
    echo "  deploy    - Deploy to production (calls deployment script)"
    echo "  help      - Show this help message"
    echo
    echo "Workflow:"
    echo "1. Always work in: $DEV_DIR"
    echo "2. Test changes on: https://dev.omnilyzer.ai"
    echo "3. When ready, deploy to: https://omnilyzer.ai"
    echo
    echo "Quick Start:"
    echo "  cd $DEV_DIR"
    echo "  $0 start"
    echo "  # Make your changes"
    echo "  $0 test"
    echo "  $0 ready"
    echo "  $0 deploy"
}

# Main function
main() {
    case "${1:-status}" in
        "status")
            show_dev_status
            ;;
        "start")
            start_dev_environment
            ;;
        "test")
            test_dev_environment
            ;;
        "ready")
            check_deployment_readiness
            ;;
        "deploy")
            if check_deployment_readiness; then
                echo -e "${BLUE}Starting deployment...${NC}"
                /var/www/deploy_to_production.sh
            else
                echo -e "${RED}Deployment readiness check failed${NC}"
                exit 1
            fi
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo -e "${RED}Unknown command: $1${NC}"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
