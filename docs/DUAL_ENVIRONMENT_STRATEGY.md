# Omnilyzer Dual Environment Development Strategy

## Overview
This document establishes rules and procedures for maintaining consistent development and production environments for the Omnilyzer project.

## Environment Structure

### Development Environment
- **Location**: `/var/www/omnilyzer/`
- **Domain**: `dev.omnilyzer.ai`
- **Port**: 3000 (frontend), 3002 (backend)
- **Mode**: Development (`NODE_ENV=development`)
- **Purpose**: Active development, testing, debugging

### Production Environment  
- **Location**: `/var/www/omnilyzer-prod/`
- **Domain**: `omnilyzer.ai`
- **Port**: 3002 (production server)
- **Mode**: Production (`NODE_ENV=production`)
- **Purpose**: Live website, optimized performance

## Core Development Rules

### Rule 1: Environment-Aware Configuration
- **ALL** environment-specific settings MUST use environment variables
- **NO** hardcoded URLs, API keys, or configuration values
- **ALWAYS** provide fallbacks for missing environment variables

### Rule 2: Database Configuration
```javascript
// ✅ CORRECT - Environment-aware
const DATABASE_URL = process.env.NODE_ENV === 'production' 
  ? process.env.PROD_DATABASE_URL 
  : process.env.DEV_DATABASE_URL;

// ❌ WRONG - Hardcoded
const DATABASE_URL = "postgresql://localhost:5432/omnilyzer";
```

### Rule 3: Analytics Configuration
```javascript
// ✅ CORRECT - Environment-aware analytics
const ANALYTICS_CONFIG = {
  enabled: process.env.NODE_ENV === 'production' || process.env.FORCE_ANALYTICS === 'true',
  googleAnalyticsId: process.env.NODE_ENV === 'production' 
    ? process.env.GA_PROD_ID 
    : process.env.GA_DEV_ID,
  debug: process.env.NODE_ENV === 'development'
};

// ❌ WRONG - Environment-specific disabling
if (process.env.NODE_ENV === 'development') {
  return { success: true, message: 'Analytics disabled in development' };
}
```

### Rule 4: API Endpoints
```javascript
// ✅ CORRECT - Environment-aware URLs
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://omnilyzer.ai/api'
  : 'https://dev.omnilyzer.ai/api';

// ❌ WRONG - Hardcoded URLs
const API_BASE_URL = 'https://omnilyzer.ai/api';
```

## Deployment Workflow

### Step 1: Development Phase
1. Work in `/var/www/omnilyzer/` (development environment)
2. Test all features with development configuration
3. Ensure environment variables are properly configured
4. Test with both development AND production-like settings

### Step 2: Pre-Production Sync
1. **ALWAYS** sync development to production before building:
   ```bash
   cd /var/www
   rsync -av --delete omnilyzer/ omnilyzer-prod/ --exclude=node_modules --exclude=.next --exclude=logs
   ```

### Step 3: Production Build
1. Build in production environment:
   ```bash
   cd /var/www/omnilyzer-prod
   NODE_ENV=production npm run build
   ```

### Step 4: Production Deployment
1. Start production server:
   ```bash
   NODE_ENV=production npm start
   ```

## Environment Variables Strategy

### Required Environment Files
- `.env.local` - Local development overrides
- `.env.development` - Development environment defaults  
- `.env.production` - Production environment defaults

### Critical Environment Variables
```bash
# Database
DEV_DATABASE_URL=postgresql://localhost:5432/omnilyzer_dev
PROD_DATABASE_URL=postgresql://localhost:5432/omnilyzer_prod

# Analytics
GA_DEV_ID=G-XXXXXXXXXX
GA_PROD_ID=G-YYYYYYYYYY
FORCE_ANALYTICS=false

# API Configuration
DEV_API_URL=https://dev.omnilyzer.ai
PROD_API_URL=https://omnilyzer.ai

# Email Configuration
DEV_SMTP_HOST=smtp.mailtrap.io
PROD_SMTP_HOST=send.one.com
```

## Code Review Checklist

Before any deployment, verify:
- [ ] No hardcoded URLs or configuration values
- [ ] All environment variables properly configured
- [ ] Analytics work in both environments
- [ ] Database connections environment-aware
- [ ] API endpoints use environment variables
- [ ] Build process works in production mode
- [ ] No development-only code in production paths

## Testing Strategy

### Development Testing
- Test with `NODE_ENV=development`
- Verify development-specific features work
- Test with development database/analytics

### Pre-Production Testing  
- Test with `NODE_ENV=production` in development environment
- Verify production configurations work
- Test build process

### Production Validation
- Verify production server starts correctly
- Test all critical functionality
- Validate analytics and tracking
- Check performance and optimization

## Emergency Procedures

### If Production Breaks
1. Immediately revert to last known working state
2. Investigate in development environment first
3. Never debug directly in production
4. Always test fixes in development before applying to production

### If Environments Drift
1. Stop both servers
2. Backup production data if needed
3. Re-sync from development to production
4. Rebuild and restart production

## Monitoring and Maintenance

### Daily Checks
- Verify both environments are running
- Check for configuration drift
- Monitor error logs in both environments

### Weekly Tasks
- Sync development to production (if needed)
- Review environment variable changes
- Update documentation for any new configurations

This strategy ensures consistent, reliable deployments while maintaining clear separation between development and production environments.
