# Social Media Integration Setup

## Overview
The blog system includes social media publishing functionality for Facebook and LinkedIn. Currently implemented as a mock system for demonstration, but can be easily configured for real API integration.

## Current Implementation
- **Mock APIs**: Simulates Facebook and LinkedIn posting with 90% and 85% success rates respectively
- **File Storage**: Social media posts are stored in `/data/social-posts.json`
- **Admin Interface**: Full UI for publishing and tracking social media posts

## Setting Up Real API Integration

### Facebook Integration

#### 1. Create Facebook App
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app for "Business"
3. Add "Facebook Login" and "Pages API" products

#### 2. Get Required Credentials
```bash
# Add to .env.local
FACEBOOK_APP_ID=your_app_id
FACEBOOK_APP_SECRET=your_app_secret
FACEBOOK_ACCESS_TOKEN=your_page_access_token
FACEBOOK_PAGE_ID=61577959296494
```

#### 3. Update API Code
In `app/api/v1/blog/social/publish/route.ts`, replace the mock `postToFacebook` function:

```typescript
async function postToFacebook(content: string, blogUrl: string) {
  const response = await fetch(`https://graph.facebook.com/v18.0/${process.env.FACEBOOK_PAGE_ID}/feed`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      message: content,
      link: blogUrl,
      access_token: process.env.FACEBOOK_ACCESS_TOKEN
    })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Facebook API error: ${error.error.message}`);
  }

  const data = await response.json();
  return {
    success: true,
    postId: data.id,
    url: `https://www.facebook.com/${process.env.FACEBOOK_PAGE_ID}/posts/${data.id}`
  };
}
```

### LinkedIn Integration

#### 1. Create LinkedIn App
1. Go to [LinkedIn Developers](https://www.linkedin.com/developers/)
2. Create a new app for your company page
3. Request access to "Share on LinkedIn" and "Marketing Developer Platform"

#### 2. Get Required Credentials
```bash
# Add to .env.local
LINKEDIN_CLIENT_ID=your_client_id
LINKEDIN_CLIENT_SECRET=your_client_secret
LINKEDIN_ACCESS_TOKEN=your_access_token
LINKEDIN_COMPANY_ID=your_company_id
```

#### 3. Update API Code
Replace the mock `postToLinkedIn` function:

```typescript
async function postToLinkedIn(content: string, blogUrl: string) {
  const response = await fetch('https://api.linkedin.com/v2/ugcPosts', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.LINKEDIN_ACCESS_TOKEN}`,
      'Content-Type': 'application/json',
      'X-Restli-Protocol-Version': '2.0.0'
    },
    body: JSON.stringify({
      author: `urn:li:organization:${process.env.LINKEDIN_COMPANY_ID}`,
      lifecycleState: 'PUBLISHED',
      specificContent: {
        'com.linkedin.ugc.ShareContent': {
          shareCommentary: {
            text: content
          },
          shareMediaCategory: 'ARTICLE',
          media: [{
            status: 'READY',
            originalUrl: blogUrl
          }]
        }
      },
      visibility: {
        'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC'
      }
    })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`LinkedIn API error: ${error.message}`);
  }

  const data = await response.json();
  return {
    success: true,
    postId: data.id,
    url: `https://www.linkedin.com/company/omnilyzer/posts/${data.id}`
  };
}
```

## Features

### Admin Interface
- **Social Button**: Appears for published blog posts only
- **Platform Selection**: Choose Facebook, LinkedIn, or both
- **Publishing History**: Track all social media posts and their status
- **Error Handling**: Shows failed posts with error messages

### Content Generation
- **Platform-Specific**: Different content formats for Facebook vs LinkedIn
- **SEO Optimized**: Includes relevant hashtags and call-to-actions
- **Brand Consistent**: All posts promote Omnilyzer services

### API Endpoints
- `POST /api/v1/blog/social/publish` - Publish to social media
- `GET /api/v1/blog/social` - Get social media posts history

## Usage

1. **Publish Blog Post**: Create and publish a blog post
2. **Access Social Media**: Click "Social" button in Blog Management
3. **Choose Platform**: Select Facebook, LinkedIn, or both
4. **Publish**: Click the platform button to publish
5. **Track Results**: View publishing history and status

## Content Templates

### Facebook Format
- Emoji-rich headlines
- Bullet points for key insights
- Hashtags: #AI #SmallBusiness #Automation
- Direct call-to-action

### LinkedIn Format
- Professional tone
- Detailed insights
- Question for engagement
- Hashtags: #ArtificialIntelligence #BusinessAutomation #SMB

## Security Notes
- Store API credentials in environment variables
- Use HTTPS for all API calls
- Implement rate limiting for social media APIs
- Log all publishing attempts for debugging

## Testing
- Mock implementation allows testing without real API calls
- 90% success rate for Facebook, 85% for LinkedIn
- Simulated delays to match real API response times
