# Dual Environment Implementation Guide

## 🎯 Quick Start

### 1. Use the Environment Management Script
```bash
# Check current status
./scripts/environment-management.sh status

# Validate configuration
./scripts/environment-management.sh validate

# Deploy to production
./scripts/environment-management.sh deploy
```

### 2. Use Environment-Aware Configuration
```typescript
import { ANALYTICS_CONFIG, getApiEndpoint } from '../lib/environment-config';

// ✅ Environment-aware analytics
if (ANALYTICS_CONFIG.ENABLED) {
  await fetch(getApiEndpoint('/v1/analytics/track/pageview'), {
    method: 'POST',
    body: JSON.stringify(data)
  });
}
```

## 📋 Daily Development Workflow

### Development Phase
1. **Work in development environment**: `/var/www/omnilyzer/`
2. **Use environment-aware code**: Import from `lib/environment-config.ts`
3. **Test with both configurations**: 
   - `NODE_ENV=development npm run dev`
   - `NODE_ENV=production npm run build && npm start`

### Deployment Phase
1. **Validate configuration**: `./scripts/environment-management.sh validate`
2. **Deploy to production**: `./scripts/environment-management.sh deploy`
3. **Verify deployment**: `./scripts/environment-management.sh status`

## 🔧 Configuration Setup

### Environment Variables
1. Copy templates:
   ```bash
   cp .env.development.template .env.development
   cp .env.production.template .env.production
   ```

2. Customize values for your environment

3. Use in code:
   ```typescript
   import { APP_CONFIG, ANALYTICS_CONFIG } from '../lib/environment-config';
   ```

## 🚫 What NOT to Do

### ❌ Hardcoded URLs
```typescript
const url = 'https://omnilyzer.ai/api/v1/analytics';
```

### ❌ Environment-specific disabling
```typescript
if (process.env.NODE_ENV === 'development') {
  return { success: true, message: 'Disabled in development' };
}
```

### ❌ Inline environment checks
```typescript
const config = process.env.NODE_ENV === 'production' ? prodConfig : devConfig;
```

## ✅ What TO Do

### ✅ Environment-aware configuration
```typescript
import { getApiEndpoint } from '../lib/environment-config';
const url = getApiEndpoint('/v1/analytics');
```

### ✅ Feature flags
```typescript
import { isFeatureEnabled } from '../lib/environment-config';
if (isFeatureEnabled('ADVANCED_ANALYTICS')) {
  // Process analytics
}
```

### ✅ Centralized configuration
```typescript
import { ANALYTICS_CONFIG } from '../lib/environment-config';
const enabled = ANALYTICS_CONFIG.ENABLED;
```

## 🔍 Code Review Checklist

Before committing code, ensure:
- [ ] No hardcoded URLs or API endpoints
- [ ] Environment variables used for configuration
- [ ] Feature flags used instead of environment checks
- [ ] Code works in both development and production
- [ ] Analytics and tracking work in both environments
- [ ] Database connections use environment variables
- [ ] Email configuration uses environment variables
- [ ] Rate limiting uses environment-aware settings

## 🚀 Deployment Commands

### Quick Commands
```bash
# Full deployment
./scripts/environment-management.sh deploy

# Just sync files
./scripts/environment-management.sh sync

# Just build production
./scripts/environment-management.sh build

# Just start production server
./scripts/environment-management.sh start
```

### Manual Deployment
```bash
# 1. Sync development to production
cd /var/www
rsync -av --delete omnilyzer/ omnilyzer-prod/ --exclude=node_modules --exclude=.next --exclude=logs

# 2. Build production
cd /var/www/omnilyzer-prod
NODE_ENV=production npm run build

# 3. Start production server
NODE_ENV=production npm start
```

## 🔧 Troubleshooting

### Build Fails
1. Check TypeScript errors: `npm run type-check`
2. Validate environment: `./scripts/environment-management.sh validate`
3. Clear cache: `rm -rf .next && npm run build`

### Analytics Not Working
1. Check environment variables: `echo $NODE_ENV`
2. Test API endpoints: `curl -X POST https://omnilyzer.ai/api/v1/analytics/track/pageview`
3. Check server logs: `tail -f logs/production.log`

### Environment Drift
1. Stop both servers
2. Re-sync: `./scripts/environment-management.sh sync`
3. Rebuild: `./scripts/environment-management.sh build`
4. Restart: `./scripts/environment-management.sh start`

## 📊 Current Status

After implementing this strategy:
- ✅ **Production server**: Running in production mode
- ✅ **Analytics**: Working in both environments
- ✅ **Environment management**: Automated scripts available
- ✅ **Configuration**: Centralized and environment-aware
- ✅ **Deployment**: Streamlined workflow established

## 🎯 Next Steps

1. **Refactor existing code** using the examples in `ENVIRONMENT_REFACTORING_EXAMPLES.md`
2. **Set up proper environment variables** using the templates
3. **Test the deployment workflow** with the management scripts
4. **Establish monitoring** for both environments
5. **Document any new configurations** as they're added

This dual environment strategy ensures consistent, reliable deployments while maintaining clear separation between development and production environments.
