# Environment-Aware Code Refactoring Examples

This document shows how to refactor existing code to be environment-aware using our dual environment strategy.

## Analytics Code Refactoring

### ❌ BEFORE (Environment-specific disabling)
```typescript
export async function POST(request: NextRequest) {
  try {
    // In development mode, just return success without processing
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json({ success: true, message: 'Analytics disabled in development' });
    }

    const body = await request.json();
    console.log('Analytics pageview:', body);
    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json({ success: false, error: 'Analytics error' }, { status: 500 });
  }
}
```

### ✅ AFTER (Environment-aware configuration)
```typescript
import { ANALYTICS_CONFIG, envLog } from '../../../lib/environment-config';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Log analytics data (works in both environments)
    if (ANALYTICS_CONFIG.DEBUG) {
      envLog(`Analytics pageview: ${JSON.stringify(body)}`, 'debug');
    }
    
    // Process analytics if enabled
    if (ANALYTICS_CONFIG.ENABLED) {
      await processAnalyticsData(body);
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Analytics tracked successfully',
      environment: process.env.NODE_ENV,
      processed: ANALYTICS_CONFIG.ENABLED
    });
  } catch (error) {
    envLog(`Analytics error: ${error}`, 'error');
    return NextResponse.json({ success: false, error: 'Analytics error' }, { status: 500 });
  }
}
```

## Database Connection Refactoring

### ❌ BEFORE (Hardcoded configuration)
```typescript
const pool = new Pool({
  connectionString: 'postgresql://localhost:5432/omnilyzer',
  max: 20,
});
```

### ✅ AFTER (Environment-aware configuration)
```typescript
import { APP_CONFIG } from '../lib/environment-config';

const pool = new Pool({
  connectionString: APP_CONFIG.DATABASE_URL,
  max: APP_CONFIG.DATABASE_POOL_SIZE,
});
```

## API Client Refactoring

### ❌ BEFORE (Hardcoded URLs)
```typescript
const response = await fetch('https://omnilyzer.ai/api/v1/analytics/track/pageview', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(data),
});
```

### ✅ AFTER (Environment-aware URLs)
```typescript
import { getApiEndpoint } from '../lib/environment-config';

const response = await fetch(getApiEndpoint('/v1/analytics/track/pageview'), {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(data),
});
```

## Email Configuration Refactoring

### ❌ BEFORE (Environment-specific code)
```typescript
const emailConfig = process.env.NODE_ENV === 'production' 
  ? {
      host: 'send.one.com',
      port: 2525,
      auth: { user: '<EMAIL>', pass: 'kbtu dujl gzsy mhng' }
    }
  : {
      host: 'smtp.mailtrap.io',
      port: 2525,
      auth: { user: 'dev_user', pass: 'dev_pass' }
    };
```

### ✅ AFTER (Centralized configuration)
```typescript
import { EMAIL_CONFIG } from '../lib/environment-config';

const emailConfig = {
  host: EMAIL_CONFIG.SMTP_HOST,
  port: EMAIL_CONFIG.SMTP_PORT,
  auth: {
    user: EMAIL_CONFIG.SMTP_USER,
    pass: EMAIL_CONFIG.SMTP_PASS
  }
};
```

## Component Refactoring for Environment Awareness

### ❌ BEFORE (Hardcoded behavior)
```typescript
export default function AnalyticsDashboard() {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    // Always try to fetch analytics
    fetch('/api/v1/analytics/dashboard')
      .then(res => res.json())
      .then(setData);
  }, []);

  return <div>{/* render data */}</div>;
}
```

### ✅ AFTER (Environment-aware behavior)
```typescript
import { ANALYTICS_CONFIG, isFeatureEnabled } from '../lib/environment-config';

export default function AnalyticsDashboard() {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    if (!isFeatureEnabled('ADVANCED_ANALYTICS')) {
      setLoading(false);
      return;
    }
    
    fetch('/api/v1/analytics/dashboard')
      .then(res => res.json())
      .then(setData)
      .finally(() => setLoading(false));
  }, []);

  if (!isFeatureEnabled('ADVANCED_ANALYTICS')) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">
        <p>Analytics dashboard is disabled in this environment.</p>
      </div>
    );
  }

  if (loading) return <div>Loading...</div>;
  
  return <div>{/* render data */}</div>;
}
```

## Rate Limiting Refactoring

### ❌ BEFORE (Fixed rate limits)
```typescript
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
});
```

### ✅ AFTER (Environment-aware rate limits)
```typescript
import { RATE_LIMIT_CONFIG } from '../lib/environment-config';

const limiter = rateLimit({
  windowMs: RATE_LIMIT_CONFIG.WINDOW_MS,
  max: RATE_LIMIT_CONFIG.MAX_REQUESTS,
});
```

## Logging Refactoring

### ❌ BEFORE (Inconsistent logging)
```typescript
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info:', data);
}
console.error('Error occurred:', error);
```

### ✅ AFTER (Environment-aware logging)
```typescript
import { envLog } from '../lib/environment-config';

envLog(`Debug info: ${JSON.stringify(data)}`, 'debug');
envLog(`Error occurred: ${error}`, 'error');
```

## Feature Flag Usage

### ❌ BEFORE (Hardcoded feature checks)
```typescript
const showChatbot = process.env.NODE_ENV === 'production';
```

### ✅ AFTER (Feature flag system)
```typescript
import { isFeatureEnabled } from '../lib/environment-config';

const showChatbot = isFeatureEnabled('CHATBOT_ENABLED');
```

## Migration Checklist

When refactoring existing code:

- [ ] Replace hardcoded URLs with environment variables
- [ ] Use centralized configuration instead of inline environment checks
- [ ] Replace environment-specific disabling with feature flags
- [ ] Use environment-aware logging
- [ ] Update database connections to use environment variables
- [ ] Replace hardcoded API keys with environment variables
- [ ] Use feature flags instead of environment-based feature toggling
- [ ] Test in both development and production modes
- [ ] Update documentation and comments

## Testing Your Refactored Code

1. **Development Testing**:
   ```bash
   NODE_ENV=development npm run dev
   ```

2. **Production-like Testing**:
   ```bash
   NODE_ENV=production npm run build && npm start
   ```

3. **Configuration Validation**:
   ```bash
   ./scripts/environment-management.sh validate
   ```

This approach ensures your code works consistently across environments while maintaining appropriate differences in configuration and behavior.
