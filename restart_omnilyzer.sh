#!/bin/bash

# Omnilyzer Safe Restart Script
# This script safely restarts Omnilyzer services without crashing SSH or the server
# Author: Omnilyzer Development Team
# Version: 1.0

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Configuration
SCRIPT_DIR="/var/www"
DEV_DIR="/var/www/omnilyzer"
PROD_DIR="/var/www/omnilyzer-prod"
LOG_DIR="/var/www/logs"
LOG_FILE="$LOG_DIR/omnilyzer_restart.log"
TIMEOUT=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local msg="${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
    echo -e "$msg"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE" 2>/dev/null || true
}

error() {
    local msg="${RED}[ERROR]${NC} $1"
    echo -e "$msg"
    echo "[ERROR] $1" >> "$LOG_FILE" 2>/dev/null || true
}

success() {
    local msg="${GREEN}[SUCCESS]${NC} $1"
    echo -e "$msg"
    echo "[SUCCESS] $1" >> "$LOG_FILE" 2>/dev/null || true
}

warning() {
    local msg="${YELLOW}[WARNING]${NC} $1"
    echo -e "$msg"
    echo "[WARNING] $1" >> "$LOG_FILE" 2>/dev/null || true
}

# Function to brutally kill processes on a specific port
kill_port() {
    local port=$1
    local service_name=$2
    
    log "Killing all processes on port $port ($service_name)..."
    
    # Find PIDs using the port
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    
    if [ -n "$pids" ]; then
        log "Found processes on port $port: $pids"
        
        # First try graceful termination
        echo "$pids" | xargs -r kill -TERM 2>/dev/null || true
        sleep 2
        
        # Check if still running and force kill
        local remaining_pids=$(lsof -ti:$port 2>/dev/null || true)
        if [ -n "$remaining_pids" ]; then
            warning "Processes still running, force killing..."
            echo "$remaining_pids" | xargs -r kill -KILL 2>/dev/null || true
            sleep 1
        fi
        
        # Final check
        local final_pids=$(lsof -ti:$port 2>/dev/null || true)
        if [ -n "$final_pids" ]; then
            error "Failed to kill all processes on port $port"
            return 1
        else
            success "Successfully killed all processes on port $port"
        fi
    else
        log "No processes found on port $port"
    fi
}

# Function to wait for port to be free
wait_for_port_free() {
    local port=$1
    local max_wait=$2
    local count=0
    
    while [ $count -lt $max_wait ]; do
        if ! lsof -ti:$port >/dev/null 2>&1; then
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    return 1
}

# Function to wait for service to be ready
wait_for_service() {
    local port=$1
    local service_name=$2
    local max_wait=$3
    local count=0
    
    log "Waiting for $service_name to be ready on port $port..."
    
    while [ $count -lt $max_wait ]; do
        if curl -s -f "http://localhost:$port" >/dev/null 2>&1; then
            success "$service_name is ready on port $port"
            return 0
        fi
        sleep 2
        count=$((count + 2))
        echo -n "."
    done
    echo
    error "$service_name failed to start within $max_wait seconds"
    return 1
}

# Function to start development server
start_dev_server() {
    log "Starting development server (port 3000)..."
    
    cd "$DEV_DIR"
    
    # Start in background with nohup to prevent SSH disconnection issues
    nohup npm run dev > $LOG_DIR/omnilyzer-dev.log 2>&1 &
    local dev_pid=$!
    
    log "Development server started with PID: $dev_pid"
    
    # Wait for service to be ready
    if wait_for_service 3000 "Development Server" $TIMEOUT; then
        success "Development server is running successfully"
        return 0
    else
        error "Development server failed to start"
        return 1
    fi
}

# Function to start production server
start_prod_server() {
    log "Starting production server (port 3002)..."
    
    cd "$PROD_DIR"
    
    # Start in background with nohup to prevent SSH disconnection issues
    # Use 2GB memory limit to prevent out-of-memory crashes during compilation
    NODE_OPTIONS="--max-old-space-size=2048" nohup npm run dev -- --port 3002 > $LOG_DIR/omnilyzer-prod.log 2>&1 &
    local prod_pid=$!
    
    log "Production server started with PID: $prod_pid"
    
    # Wait for service to be ready
    if wait_for_service 3002 "Production Server" $TIMEOUT; then
        success "Production server is running successfully"
        return 0
    else
        error "Production server failed to start"
        return 1
    fi
}

# Function to start backend server
start_backend_server() {
    log "Starting backend server (port 3001)..."
    
    cd "$DEV_DIR"
    
    # Check if server.js exists
    if [ ! -f "server.js" ]; then
        warning "server.js not found, skipping backend server"
        return 0
    fi
    
    # Start in background with nohup to prevent SSH disconnection issues
    nohup node server.js > $LOG_DIR/omnilyzer-backend.log 2>&1 &
    local backend_pid=$!
    
    log "Backend server started with PID: $backend_pid"
    
    # Wait for service to be ready
    if wait_for_service 3001 "Backend Server" $TIMEOUT; then
        success "Backend server is running successfully"
        return 0
    else
        error "Backend server failed to start"
        return 1
    fi
}

# Function to check service status
check_status() {
    log "Checking service status..."
    
    echo "Service Status:"
    echo "==============="
    
    # Check port 3000 (Development)
    if lsof -ti:3000 >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Development Server (port 3000): Running${NC}"
    else
        echo -e "${RED}❌ Development Server (port 3000): Not running${NC}"
    fi
    
    # Check port 3001 (Backend)
    if lsof -ti:3001 >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend Server (port 3001): Running${NC}"
    else
        echo -e "${RED}❌ Backend Server (port 3001): Not running${NC}"
    fi
    
    # Check port 3002 (Production)
    if lsof -ti:3002 >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Production Server (port 3002): Running${NC}"
    else
        echo -e "${RED}❌ Production Server (port 3002): Not running${NC}"
    fi
    
    echo
    echo "URLs:"
    echo "====="
    echo "🌐 Production:  https://omnilyzer.ai"
    echo "🔧 Development: https://dev.omnilyzer.ai"
    echo
    echo "Logs:"
    echo "====="
    echo "📋 Development: tail -f $LOG_DIR/omnilyzer-dev.log"
    echo "📋 Backend:     tail -f $LOG_DIR/omnilyzer-backend.log"
    echo "📋 Production:  tail -f $LOG_DIR/omnilyzer-prod.log"
    echo "📋 Restart:     tail -f $LOG_DIR/omnilyzer_restart.log"
}

# Main execution
main() {
    log "=== Omnilyzer Safe Restart Script Started ==="
    
    # Create log directory if it doesn't exist
    mkdir -p "$LOG_DIR"
    
    # Step 1: Brutally kill existing services
    log "Step 1: Stopping existing services..."
    kill_port 3000 "Development Server"
    kill_port 3001 "Backend Server" 
    kill_port 3002 "Production Server"
    
    # Wait a moment for ports to be completely free
    log "Waiting for ports to be completely free..."
    sleep 3
    
    # Step 2: Start services
    log "Step 2: Starting services..."
    
    # Start backend first (if exists)
    start_backend_server || warning "Backend server failed to start, continuing..."
    
    # Start development server
    start_dev_server || error "Failed to start development server"
    
    # Start production server
    start_prod_server || error "Failed to start production server"
    
    # Step 3: Final status check
    log "Step 3: Final status check..."
    sleep 5
    check_status
    
    success "=== Omnilyzer Safe Restart Script Completed ==="
}

# Trap to handle script interruption
trap 'error "Script interrupted"; exit 1' INT TERM

# Check if running as root (not recommended but handle gracefully)
if [ "$EUID" -eq 0 ]; then
    warning "Running as root. This is not recommended for security reasons."
fi

# Run main function
main "$@"
