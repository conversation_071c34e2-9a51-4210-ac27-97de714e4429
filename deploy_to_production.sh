#!/bin/bash

# Omnilyzer Development to Production Deployment Script
# This script safely deploys changes from development to production

set -euo pipefail

# Configuration
DEV_DIR="/var/www/omnilyzer"
PROD_DIR="/var/www/omnilyzer-prod"
BACKUP_DIR="/var/www/backups"
LOG_FILE="/var/www/logs/deployment.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Function to create backup
create_backup() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_path="$BACKUP_DIR/omnilyzer-prod_$timestamp"
    
    log "Creating backup of production server..."
    mkdir -p "$BACKUP_DIR"
    
    # Create backup excluding large directories
    rsync -av --exclude='node_modules' --exclude='.next' --exclude='logs' \
        "$PROD_DIR/" "$backup_path/"
    
    success "Backup created at: $backup_path"
    echo "$backup_path" > "$BACKUP_DIR/latest_backup.txt"
}

# Function to verify development server
verify_development() {
    log "Verifying development server..."
    
    if [ ! -d "$DEV_DIR" ]; then
        error "Development directory not found: $DEV_DIR"
        exit 1
    fi
    
    # Check if development server is running
    if ! curl -s -f "https://dev.omnilyzer.ai" >/dev/null; then
        warning "Development server may not be running or accessible"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    success "Development server verified"
}

# Function to sync specific directories
sync_directories() {
    log "Syncing directories from development to production..."
    
    # Directories to sync
    local dirs_to_sync=(
        "components"
        "lib"
        "utils"
        "app"
        "public"
        "styles"
        "hooks"
        "contexts"
    )
    
    for dir in "${dirs_to_sync[@]}"; do
        if [ -d "$DEV_DIR/$dir" ]; then
            log "Syncing $dir..."
            rsync -av --delete "$DEV_DIR/$dir/" "$PROD_DIR/$dir/"
            success "Synced $dir"
        else
            warning "Directory $dir not found in development, skipping"
        fi
    done
}

# Function to sync specific files
sync_files() {
    log "Syncing specific files..."
    
    # Files to sync
    local files_to_sync=(
        "package.json"
        "next.config.js"
        "tailwind.config.js"
        "tsconfig.json"
        "README.md"
    )
    
    for file in "${files_to_sync[@]}"; do
        if [ -f "$DEV_DIR/$file" ]; then
            log "Syncing $file..."
            cp "$DEV_DIR/$file" "$PROD_DIR/$file"
            success "Synced $file"
        else
            warning "File $file not found in development, skipping"
        fi
    done
}

# Function to update production environment
update_production_env() {
    log "Updating production environment..."
    
    # Ensure production .env has correct NODE_ENV
    if [ -f "$PROD_DIR/.env" ]; then
        # Update NODE_ENV to production
        sed -i 's/NODE_ENV=development/NODE_ENV=production/g' "$PROD_DIR/.env"
        success "Updated NODE_ENV to production"
    else
        warning "No .env file found in production directory"
    fi
}

# Function to install dependencies
install_dependencies() {
    log "Installing/updating dependencies in production..."
    
    cd "$PROD_DIR"
    
    # Check if package.json changed
    if ! cmp -s "$DEV_DIR/package.json" "$PROD_DIR/package.json.bak" 2>/dev/null; then
        log "package.json changed, updating dependencies..."
        cp package.json package.json.bak
        npm install
        success "Dependencies updated"
    else
        log "package.json unchanged, skipping dependency update"
    fi
}

# Function to restart services
restart_services() {
    log "Restarting production services..."
    
    cd /var/www
    if [ -f "./restart_omnilyzer.sh" ]; then
        ./restart_omnilyzer.sh
        success "Services restarted"
    else
        warning "Restart script not found, manual restart may be needed"
    fi
}

# Function to verify deployment
verify_deployment() {
    log "Verifying deployment..."
    
    # Wait for services to start
    sleep 10
    
    # Test production server
    if curl -s -f "https://omnilyzer.ai" >/dev/null; then
        success "Production server is responding"
    else
        error "Production server is not responding"
        return 1
    fi
    
    # Test API endpoints
    if curl -s -f "https://omnilyzer.ai/api/chatbot" >/dev/null; then
        success "Chatbot API is responding"
    else
        warning "Chatbot API may not be responding"
    fi
    
    # Test frontend logging
    local log_test=$(curl -s -X POST -H "Content-Type: application/json" \
        -d '{"logs":[{"level":"info","message":"Deployment test"}]}' \
        "https://omnilyzer.ai/api/v1/logs/frontend")
    
    if echo "$log_test" | grep -q "success"; then
        success "Frontend logging API is working"
    else
        warning "Frontend logging API may not be working"
    fi
}

# Function to rollback deployment
rollback_deployment() {
    error "Deployment failed, initiating rollback..."
    
    if [ -f "$BACKUP_DIR/latest_backup.txt" ]; then
        local backup_path=$(cat "$BACKUP_DIR/latest_backup.txt")
        if [ -d "$backup_path" ]; then
            log "Rolling back to: $backup_path"
            rsync -av --delete "$backup_path/" "$PROD_DIR/"
            restart_services
            success "Rollback completed"
        else
            error "Backup directory not found: $backup_path"
        fi
    else
        error "No backup information found"
    fi
}

# Main deployment function
main() {
    log "=== Starting Omnilyzer Deployment ==="
    
    # Create logs directory
    mkdir -p /var/www/logs
    
    # Verify development server
    verify_development
    
    # Create backup
    create_backup
    
    # Sync directories and files
    sync_directories
    sync_files
    
    # Update environment
    update_production_env
    
    # Install dependencies
    install_dependencies
    
    # Restart services
    restart_services
    
    # Verify deployment
    if verify_deployment; then
        success "=== Deployment completed successfully ==="
        log "Production server: https://omnilyzer.ai"
        log "Development server: https://dev.omnilyzer.ai"
    else
        error "=== Deployment verification failed ==="
        read -p "Rollback deployment? (Y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Nn]$ ]]; then
            rollback_deployment
        fi
        exit 1
    fi
}

# Handle script interruption
trap 'error "Deployment interrupted"; exit 1' INT TERM

# Check if running as root (not recommended)
if [ "$EUID" -eq 0 ]; then
    warning "Running as root. This is not recommended for security reasons."
fi

# Parse command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        rollback_deployment
        ;;
    "verify")
        verify_deployment
        ;;
    "backup")
        create_backup
        ;;
    *)
        echo "Usage: $0 [deploy|rollback|verify|backup]"
        echo "  deploy   - Deploy development to production (default)"
        echo "  rollback - Rollback to last backup"
        echo "  verify   - Verify current deployment"
        echo "  backup   - Create backup only"
        exit 1
        ;;
esac
