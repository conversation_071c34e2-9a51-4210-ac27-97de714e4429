#!/bin/bash

# Omnilyzer Unified Startup Script
# Combines startup and restart functionality with automatic environment detection
# Handles both development and production environments with port monitoring

set -euo pipefail

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly WHITE='\033[1;37m'
readonly BOLD='\033[1m'
readonly NC='\033[0m' # No Color

# Configuration
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
readonly LOG_DIR="$PROJECT_ROOT/logs"
readonly PID_DIR="$PROJECT_ROOT/pids"

# Environment detection
readonly DEV_DIR="/var/www/omnilyzer"
readonly PROD_DIR="/var/www/omnilyzer-prod"

# Port configuration
readonly DEV_FRONTEND_PORT=3000
readonly DEV_BACKEND_PORT=3001
readonly PROD_FRONTEND_PORT=3002
readonly PROD_BACKEND_PORT=3001

# Timeout settings
readonly STARTUP_TIMEOUT=60
readonly PORT_CHECK_TIMEOUT=30
readonly VERBOSE_RESTART_THRESHOLD=2

# Global variables
CURRENT_ENV=""
CURRENT_DIR=""
FRONTEND_PORT=""
BACKEND_PORT=""
RESTART_ATTEMPTS=0
VERBOSE_MODE=false
PARSED_COMMAND=""

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')]${NC} $1"
}

log_info() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')] INFO:${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARN:${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] ERROR:${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] SUCCESS:${NC} $1"
}

log_verbose() {
    if [[ "$VERBOSE_MODE" == "true" ]]; then
        echo -e "${PURPLE}[$(date +'%H:%M:%S')] VERBOSE:${NC} $1"
    fi
}

# Environment detection function
detect_environment() {
    local current_path="$(pwd)"
    
    if [[ "$current_path" == "$PROD_DIR"* ]]; then
        CURRENT_ENV="production"
        CURRENT_DIR="$PROD_DIR"
        FRONTEND_PORT="$PROD_FRONTEND_PORT"
        BACKEND_PORT="$PROD_BACKEND_PORT"
        log_info "Detected ${BOLD}PRODUCTION${NC} environment"
    elif [[ "$current_path" == "$DEV_DIR"* ]]; then
        CURRENT_ENV="development"
        CURRENT_DIR="$DEV_DIR"
        FRONTEND_PORT="$DEV_FRONTEND_PORT"
        BACKEND_PORT="$DEV_BACKEND_PORT"
        log_info "Detected ${BOLD}DEVELOPMENT${NC} environment"
    else
        # Default to development if we can't determine
        CURRENT_ENV="development"
        CURRENT_DIR="$DEV_DIR"
        FRONTEND_PORT="$DEV_FRONTEND_PORT"
        BACKEND_PORT="$DEV_BACKEND_PORT"
        log_warn "Could not detect environment from path, defaulting to ${BOLD}DEVELOPMENT${NC}"
    fi
    
    log_info "Environment: ${CYAN}$CURRENT_ENV${NC}"
    log_info "Directory: ${CYAN}$CURRENT_DIR${NC}"
    log_info "Frontend Port: ${CYAN}$FRONTEND_PORT${NC}"
    log_info "Backend Port: ${CYAN}$BACKEND_PORT${NC}"
}

# Create necessary directories
setup_directories() {
    log_verbose "Setting up directories..."
    mkdir -p "$LOG_DIR" "$PID_DIR"
    
    # Ensure log files exist
    touch "$LOG_DIR/frontend.log" "$LOG_DIR/backend.log" "$LOG_DIR/startup.log"
    
    log_verbose "Directories created: $LOG_DIR, $PID_DIR"
}

# Port checking functions
check_port() {
    local port=$1
    netstat -tuln 2>/dev/null | grep -q ":$port " || ss -tuln 2>/dev/null | grep -q ":$port "
}

get_port_pid() {
    local port=$1
    local pid=$(lsof -ti:$port 2>/dev/null | head -1)
    echo "$pid"
}

# Enhanced port killing with better process identification
kill_port_processes() {
    local port=$1
    local service_name=${2:-"Service"}
    
    log_info "Checking port $port for $service_name..."
    
    if check_port "$port"; then
        local pids=$(lsof -ti:$port 2>/dev/null || true)
        
        if [[ -n "$pids" ]]; then
            log_warn "Port $port is in use by $service_name. Killing processes..."
            
            for pid in $pids; do
                local process_info=$(ps -p "$pid" -o comm= 2>/dev/null || echo "unknown")
                log_verbose "Killing PID $pid ($process_info) on port $port"
                
                # Try graceful shutdown first
                kill -TERM "$pid" 2>/dev/null || true
                sleep 2
                
                # Force kill if still running
                if kill -0 "$pid" 2>/dev/null; then
                    log_verbose "Force killing PID $pid"
                    kill -KILL "$pid" 2>/dev/null || true
                fi
            done
            
            # Wait for port to be free
            local count=0
            while check_port "$port" && [[ $count -lt 10 ]]; do
                sleep 1
                ((count++))
            done
            
            if check_port "$port"; then
                log_error "Failed to free port $port after 10 seconds"
                return 1
            else
                log_success "Port $port freed successfully"
            fi
        fi
    else
        log_verbose "Port $port is already free"
    fi
}

# Stop all services
stop_services() {
    log "🛑 Stopping all Omnilyzer services..."
    
    # Kill processes on all possible ports
    kill_port_processes "$DEV_FRONTEND_PORT" "Development Frontend"
    kill_port_processes "$DEV_BACKEND_PORT" "Backend"
    kill_port_processes "$PROD_FRONTEND_PORT" "Production Frontend"
    
    # Additional cleanup for any remaining processes
    pkill -f "next.*dev" 2>/dev/null || true
    pkill -f "next.*start" 2>/dev/null || true
    pkill -f "server/index.js" 2>/dev/null || true
    pkill -f "server_minimal.js" 2>/dev/null || true
    pkill -f "nodemon.*server" 2>/dev/null || true
    
    # Clean up PID files
    rm -f "$PID_DIR"/*.pid 2>/dev/null || true
    
    log_success "All services stopped"
    sleep 3
}

# Environment setup
setup_environment() {
    log_info "Setting up $CURRENT_ENV environment..."
    
    cd "$CURRENT_DIR"
    
    # Set environment variables based on detected environment
    if [[ "$CURRENT_ENV" == "production" ]]; then
        export NODE_ENV=production
        export NODE_OPTIONS="--max-old-space-size=1024"
        log_verbose "Set NODE_ENV=production with 1GB memory limit"
    else
        export NODE_ENV=development
        export NODE_OPTIONS="--max-old-space-size=2048"
        log_verbose "Set NODE_ENV=development with 2GB memory limit"
    fi
    
    # Load environment-specific configuration
    if [[ -f ".env.$CURRENT_ENV" ]]; then
        log_verbose "Loading .env.$CURRENT_ENV"
        set -a
        source ".env.$CURRENT_ENV"
        set +a
    elif [[ -f ".env" ]]; then
        log_verbose "Loading .env"
        set -a
        source ".env"
        set +a
    fi
    
    # Ensure dependencies are installed
    if [[ ! -d "node_modules" ]]; then
        log_info "Installing dependencies..."
        npm install
    fi
    
    log_success "Environment setup complete"
}

# Start backend service
start_backend() {
    log_info "🔧 Starting backend service..."

    cd "$CURRENT_DIR"

    # Choose backend startup method based on environment and available files
    local backend_cmd=""
    local log_file="$LOG_DIR/backend-$CURRENT_ENV.log"

    if [[ -f "server_minimal.js" ]]; then
        backend_cmd="node server_minimal.js"
        log_verbose "Using server_minimal.js for backend"
    elif [[ -f "server/index.js" ]]; then
        backend_cmd="node server/index.js"
        log_verbose "Using server/index.js for backend"
    else
        backend_cmd="npm run server"
        log_verbose "Using npm run server for backend"
    fi

    # Start backend with appropriate logging
    if [[ "$VERBOSE_MODE" == "true" ]]; then
        log_verbose "Starting backend with command: $backend_cmd"
        $backend_cmd > "$log_file" 2>&1 &
    else
        nohup $backend_cmd > "$log_file" 2>&1 &
    fi

    local backend_pid=$!
    echo "$backend_pid" > "$PID_DIR/backend.pid"

    log_info "Backend started with PID: $backend_pid"

    # Wait for backend to be ready
    local count=0
    while [[ $count -lt $STARTUP_TIMEOUT ]]; do
        if check_port "$BACKEND_PORT"; then
            log_success "✅ Backend is ready on port $BACKEND_PORT"
            return 0
        fi
        sleep 2
        ((count+=2))
        log_verbose "Waiting for backend... ($count/$STARTUP_TIMEOUT seconds)"
    done

    log_error "❌ Backend failed to start within $STARTUP_TIMEOUT seconds"
    return 1
}

# Start frontend service
start_frontend() {
    log_info "🎨 Starting frontend service..."

    cd "$CURRENT_DIR"

    local frontend_cmd=""
    local log_file="$LOG_DIR/frontend-$CURRENT_ENV.log"

    if [[ "$CURRENT_ENV" == "production" ]]; then
        # Production: build first, then start
        log_info "Building production frontend..."
        if [[ "$VERBOSE_MODE" == "true" ]]; then
            npm run build
        else
            npm run build > "$LOG_DIR/build-$CURRENT_ENV.log" 2>&1
        fi

        frontend_cmd="npm start"
        log_verbose "Using npm start for production frontend"
    else
        # Development: use dev server
        frontend_cmd="npm run dev"
        log_verbose "Using npm run dev for development frontend"
    fi

    # Start frontend with appropriate logging
    if [[ "$VERBOSE_MODE" == "true" ]]; then
        log_verbose "Starting frontend with command: $frontend_cmd"
        $frontend_cmd > "$log_file" 2>&1 &
    else
        nohup $frontend_cmd > "$log_file" 2>&1 &
    fi

    local frontend_pid=$!
    echo "$frontend_pid" > "$PID_DIR/frontend.pid"

    log_info "Frontend started with PID: $frontend_pid"

    # Wait for frontend to be ready
    local count=0
    while [[ $count -lt $STARTUP_TIMEOUT ]]; do
        if check_port "$FRONTEND_PORT"; then
            log_success "✅ Frontend is ready on port $FRONTEND_PORT"
            return 0
        fi
        sleep 2
        ((count+=2))
        log_verbose "Waiting for frontend... ($count/$STARTUP_TIMEOUT seconds)"
    done

    log_error "❌ Frontend failed to start within $STARTUP_TIMEOUT seconds"
    return 1
}

# Comprehensive port status check
show_port_status() {
    log "📊 Port Status Overview:"
    echo

    # Check all relevant ports
    local ports=("$DEV_FRONTEND_PORT" "$DEV_BACKEND_PORT" "$PROD_FRONTEND_PORT")
    local port_names=("Dev Frontend" "Backend" "Prod Frontend")

    for i in "${!ports[@]}"; do
        local port="${ports[$i]}"
        local name="${port_names[$i]}"

        if check_port "$port"; then
            local pid=$(get_port_pid "$port")
            local process_info=$(ps -p "$pid" -o comm= 2>/dev/null || echo "unknown")
            echo -e "  ${GREEN}✅ Port $port${NC} - ${BOLD}$name${NC} (PID: $pid, Process: $process_info)"
        else
            echo -e "  ${RED}❌ Port $port${NC} - ${BOLD}$name${NC} (Not listening)"
        fi
    done

    echo

    # Show environment-specific URLs
    if [[ "$CURRENT_ENV" == "production" ]]; then
        echo -e "🌐 ${BOLD}Production URLs:${NC}"
        echo -e "  Frontend: ${CYAN}https://omnilyzer.ai${NC}"
        echo -e "  Backend:  ${CYAN}https://omnilyzer.ai/api${NC}"
    else
        echo -e "🌐 ${BOLD}Development URLs:${NC}"
        echo -e "  Frontend: ${CYAN}https://dev.omnilyzer.ai${NC}"
        echo -e "  Backend:  ${CYAN}https://dev.omnilyzer.ai/api${NC}"
        echo -e "  Local:    ${CYAN}http://localhost:$FRONTEND_PORT${NC}"
    fi

    echo
}

# Verify services are working
verify_services() {
    log_info "🔍 Verifying services..."

    local frontend_ok=false
    local backend_ok=false

    # Check frontend
    if check_port "$FRONTEND_PORT"; then
        if curl -s -f "http://localhost:$FRONTEND_PORT" >/dev/null 2>&1; then
            frontend_ok=true
            log_success "Frontend is responding"
        else
            log_warn "Frontend port is open but not responding to HTTP requests"
        fi
    else
        log_error "Frontend is not listening on port $FRONTEND_PORT"
    fi

    # Check backend
    if check_port "$BACKEND_PORT"; then
        if curl -s -f "http://localhost:$BACKEND_PORT/api/v1/health" >/dev/null 2>&1; then
            backend_ok=true
            log_success "Backend is responding"
        else
            log_warn "Backend port is open but health check failed"
        fi
    else
        log_error "Backend is not listening on port $BACKEND_PORT"
    fi

    if [[ "$frontend_ok" == "true" && "$backend_ok" == "true" ]]; then
        return 0
    else
        return 1
    fi
}

# Main startup function
start_services() {
    log "🚀 Starting Omnilyzer services in $CURRENT_ENV mode..."

    # Setup
    setup_directories
    setup_environment

    # Start services
    if start_backend; then
        if start_frontend; then
            # Wait for services to stabilize
            log_info "⏳ Waiting for services to stabilize..."
            sleep 5

            # Verify everything is working
            if verify_services; then
                log_success "🎉 All services started successfully!"
                show_port_status
                return 0
            else
                log_error "❌ Service verification failed"
                return 1
            fi
        else
            log_error "❌ Frontend startup failed"
            return 1
        fi
    else
        log_error "❌ Backend startup failed"
        return 1
    fi
}

# Restart with automatic retry and verbose mode
restart_services() {
    ((RESTART_ATTEMPTS++))

    log "🔄 Restarting Omnilyzer services (attempt $RESTART_ATTEMPTS)..."

    # Enable verbose mode if we've had multiple restart attempts
    if [[ $RESTART_ATTEMPTS -ge $VERBOSE_RESTART_THRESHOLD ]]; then
        VERBOSE_MODE=true
        log_warn "Enabling verbose mode due to multiple restart attempts"
    fi

    # Stop existing services
    stop_services

    # Start services
    if start_services; then
        RESTART_ATTEMPTS=0  # Reset counter on success
        return 0
    else
        log_error "❌ Restart attempt $RESTART_ATTEMPTS failed"

        if [[ $RESTART_ATTEMPTS -lt 3 ]]; then
            log_info "Will retry with verbose output..."
            VERBOSE_MODE=true
            sleep 5
            restart_services
        else
            log_error "❌ Maximum restart attempts reached. Manual intervention required."
            show_port_status
            return 1
        fi
    fi
}

# Status check function
status_check() {
    log "📋 Omnilyzer Service Status"
    echo

    # Don't re-detect environment if already set
    if [[ -z "$CURRENT_ENV" ]]; then
        detect_environment
    fi
    show_port_status

    # Show recent log entries if verbose
    if [[ "$VERBOSE_MODE" == "true" ]]; then
        echo -e "${BOLD}Recent log entries:${NC}"
        echo

        if [[ -f "$LOG_DIR/frontend-$CURRENT_ENV.log" ]]; then
            echo -e "${CYAN}Frontend logs (last 5 lines):${NC}"
            tail -5 "$LOG_DIR/frontend-$CURRENT_ENV.log" 2>/dev/null || echo "No frontend logs found"
            echo
        fi

        if [[ -f "$LOG_DIR/backend-$CURRENT_ENV.log" ]]; then
            echo -e "${CYAN}Backend logs (last 5 lines):${NC}"
            tail -5 "$LOG_DIR/backend-$CURRENT_ENV.log" 2>/dev/null || echo "No backend logs found"
            echo
        fi
    fi
}

# Show usage information
show_usage() {
    echo -e "${BOLD}Omnilyzer Unified Startup Script${NC}"
    echo
    echo -e "${CYAN}Usage:${NC}"
    echo "  $0 [COMMAND] [OPTIONS]"
    echo
    echo -e "${CYAN}Commands:${NC}"
    echo "  start     - Start services (default)"
    echo "  restart   - Restart services"
    echo "  stop      - Stop all services"
    echo "  status    - Show service status"
    echo "  help      - Show this help message"
    echo
    echo -e "${CYAN}Options:${NC}"
    echo "  -v, --verbose    - Enable verbose output"
    echo "  -e, --env ENV    - Force specific environment (dev|prod)"
    echo
    echo -e "${CYAN}Examples:${NC}"
    echo "  $0                    # Auto-detect environment and start"
    echo "  $0 restart --verbose # Restart with verbose output"
    echo "  $0 --env prod start  # Force production environment"
    echo "  $0 status -v         # Show detailed status"
    echo
    echo -e "${CYAN}Environment Detection:${NC}"
    echo "  - Automatically detects environment based on current directory"
    echo "  - Development: /var/www/omnilyzer (ports 3000, 3001)"
    echo "  - Production:  /var/www/omnilyzer-prod (ports 3002, 3001)"
    echo
}

# Parse command line arguments
parse_arguments() {
    local command=""
    local skip_next=false

    for arg in "$@"; do
        if [[ "$skip_next" == "true" ]]; then
            skip_next=false
            continue
        fi

        case "$arg" in
            -v|--verbose)
                VERBOSE_MODE=true
                ;;
            -e|--env)
                skip_next=true
                # Find the next argument
                local found_env=false
                for next_arg in "$@"; do
                    if [[ "$found_env" == "true" ]]; then
                        case "$next_arg" in
                            dev|development)
                                CURRENT_ENV="development"
                                CURRENT_DIR="$DEV_DIR"
                                FRONTEND_PORT="$DEV_FRONTEND_PORT"
                                BACKEND_PORT="$DEV_BACKEND_PORT"
                                ;;
                            prod|production)
                                CURRENT_ENV="production"
                                CURRENT_DIR="$PROD_DIR"
                                FRONTEND_PORT="$PROD_FRONTEND_PORT"
                                BACKEND_PORT="$PROD_BACKEND_PORT"
                                ;;
                            *)
                                echo "Error: Invalid environment: $next_arg. Use 'dev' or 'prod'" >&2
                                exit 1
                                ;;
                        esac
                        break
                    fi
                    if [[ "$next_arg" == "$arg" ]]; then
                        found_env=true
                    fi
                done
                ;;
            start|restart|stop|status|help)
                if [[ -z "$command" ]]; then
                    command="$arg"
                else
                    echo "Error: Multiple commands specified" >&2
                    exit 1
                fi
                ;;
            -h|--help)
                command="help"
                ;;
            dev|development|prod|production)
                # Skip environment values
                continue
                ;;
            *)
                echo "Error: Unknown option: $arg" >&2
                show_usage
                exit 1
                ;;
        esac
    done

    # Default command is start
    if [[ -z "$command" ]]; then
        command="start"
    fi

    PARSED_COMMAND="$command"
}

# Main execution function
main() {
    # Parse command line arguments first (don't use subshell to preserve variables)
    parse_arguments "$@"
    local command="$PARSED_COMMAND"

    # Log parsed options
    if [[ "$VERBOSE_MODE" == "true" ]]; then
        log_info "Verbose mode enabled"
    fi

    # Auto-detect environment if not forced
    if [[ -z "$CURRENT_ENV" ]]; then
        detect_environment
    else
        log_info "Using forced $CURRENT_ENV environment"
        log_info "Environment: ${CYAN}$CURRENT_ENV${NC}"
        log_info "Directory: ${CYAN}$CURRENT_DIR${NC}"
        log_info "Frontend Port: ${CYAN}$FRONTEND_PORT${NC}"
        log_info "Backend Port: ${CYAN}$BACKEND_PORT${NC}"
    fi

    # Execute the requested command
    case "$command" in
        "start")
            log "🎯 Starting Omnilyzer in $CURRENT_ENV mode..."
            if start_services; then
                log_success "🎉 Startup completed successfully!"
                exit 0
            else
                log_error "❌ Startup failed"
                if [[ "$VERBOSE_MODE" != "true" ]]; then
                    log_info "💡 Try running with --verbose for more details"
                fi
                exit 1
            fi
            ;;
        "restart")
            log "🔄 Restarting Omnilyzer in $CURRENT_ENV mode..."
            if restart_services; then
                log_success "🎉 Restart completed successfully!"
                exit 0
            else
                log_error "❌ Restart failed"
                exit 1
            fi
            ;;
        "stop")
            stop_services
            exit 0
            ;;
        "status")
            status_check
            exit 0
            ;;
        "help")
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Trap signals for clean shutdown
trap 'log_warn "Received interrupt signal, stopping services..."; stop_services; exit 130' INT TERM

# Run main function with all arguments
main "$@"
