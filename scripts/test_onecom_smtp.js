#!/usr/bin/env node

/**
 * One.com SMTP Connection Test
 * Tests the One.com SMTP configuration directly
 */

const nodemailer = require('nodemailer');
require('dotenv').config();

console.log('🧪 Testing One.com SMTP Connection...');
console.log('');

// Create transporter with current configuration
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT),
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASSWORD
  },
  tls: {
    rejectUnauthorized: false
  },
  requireTLS: process.env.SMTP_REQUIRE_TLS === 'true',
  connectionTimeout: 10000, // 10 seconds
  greetingTimeout: 5000,    // 5 seconds
  socketTimeout: 10000      // 10 seconds
});

console.log('📋 Configuration:');
console.log(`   Host: ${process.env.SMTP_HOST}`);
console.log(`   Port: ${process.env.SMTP_PORT}`);
console.log(`   User: ${process.env.SMTP_USER}`);
console.log(`   Password: ${process.env.SMTP_PASSWORD ? '[SET]' : '[NOT SET]'}`);
console.log(`   Secure: ${process.env.SMTP_SECURE}`);
console.log(`   From Email: ${process.env.EMAIL_FROM}`);
console.log('');

async function testConnection() {
  try {
    console.log('🔗 Testing SMTP connection...');
    await transporter.verify();
    console.log('✅ SMTP connection successful!');

    console.log('');
    console.log('📧 Sending test email...');

    const mailOptions = {
      from: `"${process.env.EMAIL_FROM_NAME || 'Omnilyzer'}" <${process.env.EMAIL_FROM}>`,
      to: process.env.SMTP_USER, // Send to self
      subject: 'One.com SMTP Test - Connection Successful',
      text: 'This is a test email to verify One.com SMTP is working correctly.',
      html: `
        <h2>🎉 One.com SMTP Test Successful!</h2>
        <p>This email confirms that your One.com SMTP configuration is working correctly.</p>
        <p><strong>Configuration:</strong></p>
        <ul>
          <li>Host: ${process.env.SMTP_HOST}</li>
          <li>Port: ${process.env.SMTP_PORT}</li>
          <li>User: ${process.env.SMTP_USER}</li>
          <li>From: ${process.env.EMAIL_FROM}</li>
          <li>Secure: ${process.env.SMTP_SECURE}</li>
        </ul>
        <p><strong>DNS Configuration Verified:</strong></p>
        <ul>
          <li>✅ A Record: dev.omnilyzer.ai → *************</li>
          <li>✅ MX Record: admin.omnilyzer.ai → mx.one.com</li>
          <li>✅ SPF Record: v=spf1 include:_custspf.one.com ~all</li>
          <li>✅ DMARC Record: v=DMARC1; p=quarantine</li>
        </ul>
        <p>Your Omnilyzer email system is now ready!</p>
      `
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('✅ Test email sent successfully!');
    console.log(`   Message ID: ${info.messageId}`);
    console.log(`   Response: ${info.response}`);

    console.log('');
    console.log('🎯 One.com SMTP is working correctly!');
    console.log('   <NAME_EMAIL> inbox for the test email.');

  } catch (error) {
    console.log('❌ SMTP connection failed!');
    console.log(`   Error: ${error.message}`);
    console.log('');

    if (error.code === 'EAUTH') {
      console.log('🔑 Authentication Error:');
      console.log('   1. Verify SMTP_USER and SMTP_PASSWORD are correct');
      console.log('   2. <NAME_EMAIL> mailbox exists on One.com');
      console.log('   3. Verify password is correct');
    } else if (error.code === 'ECONNECTION') {
      console.log('🌐 Connection Error:');
      console.log('   1. Check firewall allows port 2525 (already configured)');
      console.log('   2. Verify send.one.com is accessible');
      console.log('   3. Check DNS resolution');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('⏰ Timeout Error:');
      console.log('   1. Network connectivity issue');
      console.log('   2. One.com server may be slow');
      console.log('   3. Try again in a few minutes');
    } else {
      console.log('🔧 Other Error:');
      console.log('   1. Check One.com SMTP settings');
      console.log('   2. Verify MX record configuration');
      console.log('   3. Review One.com account settings');
    }

    console.log('');
    console.log('📖 For help, check One.com email settings or contact support');
  }
}

// Run the test
testConnection().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
