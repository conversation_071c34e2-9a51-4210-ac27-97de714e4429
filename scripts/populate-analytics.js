const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// File paths
const ANALYTICS_DATA_FILE = path.join(process.cwd(), 'data', 'analytics.json');
const CONTACTS_DATA_FILE = path.join(process.cwd(), 'data', 'contacts.json');

// Sample data for realistic analytics
const pages = [
  '/',
  '/services',
  '/about',
  '/contact',
  '/blog',
  '/blog/ai-automation-small-business',
  '/blog/data-analytics-for-small-business',
  '/services/ai-solutions',
  '/services/business-intelligence',
  '/services/web-development',
  '/services/saas-platforms',
  '/login',
  '/register',
  '/pricing',
  '/documentation'
];

const userAgents = [
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1',
  'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1',
  'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
];

const referrers = [
  '',
  'https://google.com/search',
  'https://www.linkedin.com/',
  'https://www.facebook.com/',
  'https://twitter.com/',
  'https://github.com/',
  'https://stackoverflow.com/',
  'https://dev.to/'
];

async function ensureDataDirectory() {
  const dataDir = path.dirname(ANALYTICS_DATA_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

async function readAnalyticsData() {
  try {
    const data = await fs.readFile(ANALYTICS_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.events || [];
  } catch {
    return [];
  }
}

async function writeAnalyticsData(events) {
  await fs.writeFile(ANALYTICS_DATA_FILE, JSON.stringify({ 
    events, 
    lastUpdated: new Date().toISOString() 
  }, null, 2));
}

async function readContactsData() {
  try {
    const data = await fs.readFile(CONTACTS_DATA_FILE, 'utf8');
    const parsed = JSON.parse(data);
    return parsed.contacts || [];
  } catch {
    return [];
  }
}

async function writeContactsData(contacts) {
  await fs.writeFile(CONTACTS_DATA_FILE, JSON.stringify({ 
    contacts, 
    lastUpdated: new Date().toISOString() 
  }, null, 2));
}

function generateSessionId() {
  return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function generateUserId() {
  return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function generatePageViewEvents(count, startDate, endDate) {
  const events = [];
  const sessions = [];
  
  // Generate some sessions (users)
  for (let i = 0; i < Math.floor(count / 3); i++) {
    sessions.push({
      sessionId: generateSessionId(),
      userId: generateUserId(),
      userAgent: getRandomElement(userAgents),
      referrer: getRandomElement(referrers)
    });
  }

  for (let i = 0; i < count; i++) {
    const session = getRandomElement(sessions);
    const timestamp = new Date(startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime()));
    
    events.push({
      id: uuidv4(),
      type: 'pageview',
      path: getRandomElement(pages),
      userAgent: session.userAgent,
      referrer: session.referrer,
      sessionId: session.sessionId,
      userId: session.userId,
      timestamp: timestamp.toISOString()
    });
  }

  return events;
}

function generateContactEvents(count, startDate, endDate) {
  const events = [];
  
  for (let i = 0; i < count; i++) {
    const timestamp = new Date(startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime()));
    
    events.push({
      id: uuidv4(),
      type: 'contact',
      path: '/contact',
      userAgent: getRandomElement(userAgents),
      referrer: getRandomElement(referrers),
      sessionId: generateSessionId(),
      userId: generateUserId(),
      data: {
        formType: 'contact',
        submitted: true
      },
      timestamp: timestamp.toISOString()
    });
  }

  return events;
}

function generateSampleContacts(count) {
  const contacts = [];
  const names = ['John Doe', 'Jane Smith', 'Michael Johnson', 'Sarah Wilson', 'David Brown', 'Lisa Davis', 'Robert Miller', 'Emily Garcia'];
  const companies = ['TechCorp', 'DataSoft', 'InnovateLab', 'SmartSolutions', 'FutureTech', 'CloudWorks', 'DigitalEdge', 'NextGen Systems'];
  const statuses = ['pending', 'responded', 'resolved', 'archived'];

  for (let i = 0; i < count; i++) {
    const timestamp = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000); // Last 30 days
    
    contacts.push({
      id: i + 1,
      name: getRandomElement(names),
      email: `contact${i + 1}@example.com`,
      company: getRandomElement(companies),
      message: `Sample inquiry ${i + 1} about AI solutions and business automation.`,
      status: getRandomElement(statuses),
      createdAt: timestamp.toISOString(),
      updatedAt: timestamp.toISOString()
    });
  }

  return contacts;
}

async function populateAnalyticsData() {
  console.log('🔄 Populating analytics data...');
  
  await ensureDataDirectory();
  
  // Read existing data
  const existingEvents = await readAnalyticsData();
  const existingContacts = await readContactsData();
  
  // Generate data for the last 30 days
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  
  // Generate page view events (more recent days have more traffic)
  const newPageViews = [];
  for (let day = 0; day < 30; day++) {
    const dayStart = new Date(thirtyDaysAgo.getTime() + day * 24 * 60 * 60 * 1000);
    const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);
    
    // More traffic in recent days
    const baseTraffic = 20;
    const recentBoost = Math.max(0, (30 - day) * 2);
    const dailyPageViews = Math.floor(Math.random() * (baseTraffic + recentBoost)) + 10;
    
    newPageViews.push(...generatePageViewEvents(dailyPageViews, dayStart, dayEnd));
  }
  
  // Generate contact events (fewer than page views)
  const newContactEvents = generateContactEvents(25, thirtyDaysAgo, now);
  
  // Combine all events
  const allEvents = [...existingEvents, ...newPageViews, ...newContactEvents];
  
  // Keep only last 5000 events to prevent file from growing too large
  const limitedEvents = allEvents
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, 5000);
  
  // Generate sample contacts if none exist
  let contacts = existingContacts;
  if (contacts.length === 0) {
    contacts = generateSampleContacts(50);
  }
  
  // Write data
  await writeAnalyticsData(limitedEvents);
  await writeContactsData(contacts);
  
  console.log(`✅ Generated ${newPageViews.length} page view events`);
  console.log(`✅ Generated ${newContactEvents.length} contact events`);
  console.log(`✅ Generated ${contacts.length} sample contacts`);
  console.log(`📊 Total events in database: ${limitedEvents.length}`);
  console.log('🎉 Analytics data population complete!');
}

// Run the script
populateAnalyticsData().catch(console.error);
