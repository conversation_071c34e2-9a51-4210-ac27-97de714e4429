#!/bin/bash

# Stop Complete Development Environment Script

echo "🛑 Stopping Complete Omnilyzer Development Environment..."

# Function to kill process on port
kill_port() {
    local port=$1
    echo "🔄 Stopping processes on port $port..."
    
    # Find and kill processes using the port
    local pids=$(netstat -tlnp 2>/dev/null | grep ":$port " | awk '{print $7}' | cut -d'/' -f1 | grep -v '-')
    
    if [ -n "$pids" ]; then
        for pid in $pids; do
            if [ "$pid" != "" ] && [ "$pid" != "-" ]; then
                echo "   Killing process $pid on port $port"
                kill -TERM $pid 2>/dev/null || true
            fi
        done
        sleep 2
        
        # Force kill if still running
        for pid in $pids; do
            if [ "$pid" != "" ] && [ "$pid" != "-" ] && kill -0 $pid 2>/dev/null; then
                echo "   Force killing process $pid on port $port"
                kill -KILL $pid 2>/dev/null || true
            fi
        done
    else
        echo "   No processes found on port $port"
    fi
}

# Kill frontend and backend servers
kill_port 3000
kill_port 3001

# Also kill any npm/node processes related to the project
echo "🧹 Cleaning up any remaining npm/node processes..."
pkill -f "npm run dev" 2>/dev/null || true
pkill -f "next dev" 2>/dev/null || true
pkill -f "node.*server.*index.js" 2>/dev/null || true

sleep 2

echo "✅ Development environment stopped successfully"
echo ""
echo "💡 To start again: ./scripts/start-dev-complete.sh"
