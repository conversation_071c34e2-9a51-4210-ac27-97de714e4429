#!/bin/bash

# Complete Development Server Startup Script
# This script starts both frontend and backend servers for full functionality

echo "🚀 Starting Complete Omnilyzer Development Environment..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Function to check if port is in use
check_port() {
    local port=$1
    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to kill process on port
kill_port() {
    local port=$1
    echo "🔄 Killing processes on port $port..."
    pkill -f ":$port" 2>/dev/null || true
    sleep 2
}

# Kill existing processes
echo "🧹 Cleaning up existing processes..."
kill_port 3000
kill_port 3001

# Start backend server
echo "🔧 Starting backend server on port 3001..."
cd server
node index.js &
BACKEND_PID=$!
cd ..

# Wait for backend to start
echo "⏳ Waiting for backend server to start..."
sleep 5

# Check if backend started successfully
if check_port 3001; then
    echo "✅ Backend server started successfully on port 3001"
else
    echo "❌ Failed to start backend server"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

# Start frontend server
echo "🎨 Starting frontend development server on port 3000..."
npm run dev &
FRONTEND_PID=$!

# Wait for frontend to start
echo "⏳ Waiting for frontend server to start..."
sleep 10

# Check if frontend started successfully
if check_port 3000; then
    echo "✅ Frontend server started successfully on port 3000"
else
    echo "❌ Failed to start frontend server"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    exit 1
fi

echo ""
echo "🎉 Complete development environment is now running!"
echo ""
echo "📊 Services:"
echo "   Frontend: https://dev.omnilyzer.ai (port 3000)"
echo "   Backend:  http://localhost:3001"
echo ""
echo "🔗 Admin Dashboard: https://dev.omnilyzer.ai/admin"
echo ""
echo "💡 All admin features should now work:"
echo "   ✅ Analytics"
echo "   ✅ Advanced Analytics" 
echo "   ✅ Performance"
echo "   ✅ Security"
echo "   ✅ Database Management"
echo ""
echo "🛑 To stop all servers: Ctrl+C or run './scripts/stop-dev-complete.sh'"

# Keep script running and monitor processes
trap 'echo "🛑 Stopping all servers..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true; exit 0' INT

# Monitor processes
while true; do
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        echo "❌ Backend server stopped unexpectedly"
        kill $FRONTEND_PID 2>/dev/null || true
        exit 1
    fi
    
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "❌ Frontend server stopped unexpectedly"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
    
    sleep 5
done
