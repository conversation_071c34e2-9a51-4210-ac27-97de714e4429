#!/bin/bash

# Omnilyzer Environment Management Scripts
# Usage: ./scripts/environment-management.sh [command]

set -e

DEV_DIR="/var/www/omnilyzer"
PROD_DIR="/var/www/omnilyzer-prod"
BACKUP_DIR="/var/www/backups"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to sync development to production
sync_dev_to_prod() {
    log_info "Syncing development environment to production..."
    
    # Create backup of production
    if [ -d "$PROD_DIR" ]; then
        log_info "Creating backup of current production..."
        mkdir -p "$BACKUP_DIR"
        cp -r "$PROD_DIR" "$BACKUP_DIR/omnilyzer-prod-backup-$(date +%Y%m%d_%H%M%S)"
    fi
    
    # Sync development to production
    log_info "Syncing files..."
    rsync -av --delete \
        --exclude=node_modules \
        --exclude=.next \
        --exclude=logs \
        --exclude=.env.local \
        "$DEV_DIR/" "$PROD_DIR/"
    
    log_success "Sync completed successfully"
}

# Function to build production
build_production() {
    log_info "Building production environment..."
    
    cd "$PROD_DIR"
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        log_info "Installing dependencies..."
        npm install
    fi
    
    # Clear build cache
    log_info "Clearing build cache..."
    rm -rf .next
    
    # Build production
    log_info "Building production bundle..."
    NODE_ENV=production npm run build
    
    log_success "Production build completed"
}

# Function to start production server
start_production() {
    log_info "Starting production server..."
    
    cd "$PROD_DIR"
    
    # Kill existing processes on port 3002
    log_info "Stopping existing processes on port 3002..."
    pkill -f "next.*3002" || true
    sleep 3
    
    # Start production server
    log_info "Starting production server on port 3002..."
    NODE_ENV=production nohup npm start > logs/production.log 2>&1 &
    
    # Wait a moment and check if it started
    sleep 5
    if pgrep -f "next.*3002" > /dev/null; then
        log_success "Production server started successfully"
        log_info "Server running at: https://omnilyzer.ai"
        log_info "Logs: tail -f $PROD_DIR/logs/production.log"
    else
        log_error "Failed to start production server"
        exit 1
    fi
}

# Function to deploy (sync + build + start)
deploy_production() {
    log_info "Starting full production deployment..."
    
    sync_dev_to_prod
    build_production
    start_production
    
    log_success "Production deployment completed successfully!"
}

# Function to check environment status
check_status() {
    log_info "Checking environment status..."
    
    echo -e "\n${BLUE}Development Environment:${NC}"
    if [ -d "$DEV_DIR" ]; then
        echo "  ✓ Directory exists: $DEV_DIR"
        if pgrep -f "next.*3000" > /dev/null; then
            echo "  ✓ Development server running on port 3000"
        else
            echo "  ✗ Development server not running"
        fi
    else
        echo "  ✗ Development directory not found"
    fi
    
    echo -e "\n${BLUE}Production Environment:${NC}"
    if [ -d "$PROD_DIR" ]; then
        echo "  ✓ Directory exists: $PROD_DIR"
        if pgrep -f "next.*3002" > /dev/null; then
            echo "  ✓ Production server running on port 3002"
        else
            echo "  ✗ Production server not running"
        fi
    else
        echo "  ✗ Production directory not found"
    fi
    
    echo -e "\n${BLUE}Analytics Test:${NC}"
    if curl -s -X POST https://omnilyzer.ai/api/v1/analytics/track/pageview \
        -H "Content-Type: application/json" \
        -d '{"path":"/test"}' | grep -q "success"; then
        echo "  ✓ Analytics API responding"
    else
        echo "  ✗ Analytics API not responding"
    fi
}

# Function to validate environment configuration
validate_config() {
    log_info "Validating environment configuration..."
    
    cd "$DEV_DIR"
    
    # Check for hardcoded URLs
    log_info "Checking for hardcoded URLs..."
    if grep -r "omnilyzer\.ai" --include="*.tsx" --include="*.ts" --include="*.js" . | grep -v "env\|config\|docs"; then
        log_warning "Found potential hardcoded URLs - please review"
    else
        log_success "No hardcoded URLs found"
    fi
    
    # Check for environment variable usage
    log_info "Checking environment variable usage..."
    if grep -r "process\.env\." --include="*.tsx" --include="*.ts" --include="*.js" . > /dev/null; then
        log_success "Environment variables are being used"
    else
        log_warning "No environment variables found - consider adding configuration"
    fi
    
    # Check for development-only code in production paths
    log_info "Checking for development-only code..."
    if grep -r "NODE_ENV.*development" --include="*.tsx" --include="*.ts" --include="*.js" . | grep -v "docs\|scripts"; then
        log_warning "Found development-specific code - ensure it's properly handled"
    fi
}

# Main script logic
case "$1" in
    "sync")
        sync_dev_to_prod
        ;;
    "build")
        build_production
        ;;
    "start")
        start_production
        ;;
    "deploy")
        deploy_production
        ;;
    "status")
        check_status
        ;;
    "validate")
        validate_config
        ;;
    *)
        echo "Usage: $0 {sync|build|start|deploy|status|validate}"
        echo ""
        echo "Commands:"
        echo "  sync     - Sync development to production"
        echo "  build    - Build production environment"
        echo "  start    - Start production server"
        echo "  deploy   - Full deployment (sync + build + start)"
        echo "  status   - Check environment status"
        echo "  validate - Validate environment configuration"
        exit 1
        ;;
esac
