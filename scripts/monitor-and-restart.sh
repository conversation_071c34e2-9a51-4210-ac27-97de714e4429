#!/bin/bash

# Omnilyzer Service Monitor and Auto-Restart Script
# This script monitors the health of Omnilyzer services and restarts them if needed

LOG_FILE="/var/www/omnilyzer/logs/monitor.log"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# Function to log messages
log_message() {
    echo "[$TIMESTAMP] $1" | tee -a "$LOG_FILE"
}

# Function to check if a port is listening
check_port() {
    local port=$1
    netstat -tlnp | grep ":$port " > /dev/null 2>&1
    return $?
}

# Function to check if a URL returns 200
check_url() {
    local url=$1
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null)
    [ "$response" = "200" ]
    return $?
}

# Function to restart PM2 services
restart_services() {
    log_message "🔄 Restarting all PM2 services..."
    
    # Stop all services
    pm2 stop all
    sleep 3
    
    # Start essential services only
    pm2 start omnilyzer-dev
    pm2 start omnilyzer-frontend  
    pm2 start omnilyzer-production
    
    sleep 10
    
    # Save the configuration
    pm2 save
    
    log_message "✅ Services restarted"
}

# Function to check service health
check_services() {
    local restart_needed=false
    
    log_message "🔍 Checking service health..."
    
    # Check if ports are listening
    if ! check_port 3000; then
        log_message "❌ Port 3000 (dev frontend) not listening"
        restart_needed=true
    fi
    
    if ! check_port 3001; then
        log_message "❌ Port 3001 (backend) not listening"
        restart_needed=true
    fi
    
    if ! check_port 3002; then
        log_message "❌ Port 3002 (production) not listening"
        restart_needed=true
    fi
    
    # Check if websites are responding
    if ! check_url "https://dev.omnilyzer.ai"; then
        log_message "❌ dev.omnilyzer.ai not responding"
        restart_needed=true
    fi
    
    if ! check_url "https://omnilyzer.ai"; then
        log_message "❌ omnilyzer.ai not responding"
        restart_needed=true
    fi
    
    # Check PM2 process status
    local pm2_status=$(pm2 jlist 2>/dev/null)
    if [ $? -ne 0 ]; then
        log_message "❌ PM2 not responding"
        restart_needed=true
    else
        # Check if any processes are stopped
        local stopped_count=$(echo "$pm2_status" | jq '[.[] | select(.pm2_env.status != "online")] | length' 2>/dev/null)
        if [ "$stopped_count" -gt 0 ]; then
            log_message "❌ $stopped_count PM2 processes are not online"
            restart_needed=true
        fi
    fi
    
    if [ "$restart_needed" = true ]; then
        log_message "🚨 Service issues detected, restarting services..."
        restart_services
        
        # Wait and recheck
        sleep 15
        log_message "🔍 Rechecking after restart..."
        
        if check_port 3000 && check_port 3001 && check_port 3002; then
            log_message "✅ All ports are now listening"
        else
            log_message "❌ Some ports still not listening after restart"
        fi
        
        if check_url "https://dev.omnilyzer.ai" && check_url "https://omnilyzer.ai"; then
            log_message "✅ Both websites are now responding"
        else
            log_message "❌ Some websites still not responding after restart"
        fi
    else
        log_message "✅ All services are healthy"
    fi
}

# Function to show current status
show_status() {
    echo "🔍 Omnilyzer Service Status"
    echo "=========================="
    
    echo "📊 PM2 Processes:"
    pm2 status
    
    echo ""
    echo "🌐 Port Status:"
    echo "Port 3000 (Dev Frontend): $(check_port 3000 && echo "✅ Listening" || echo "❌ Not listening")"
    echo "Port 3001 (Backend): $(check_port 3001 && echo "✅ Listening" || echo "❌ Not listening")"
    echo "Port 3002 (Production): $(check_port 3002 && echo "✅ Listening" || echo "❌ Not listening")"
    
    echo ""
    echo "🌍 Website Status:"
    echo "dev.omnilyzer.ai: $(check_url "https://dev.omnilyzer.ai" && echo "✅ Online" || echo "❌ Offline")"
    echo "omnilyzer.ai: $(check_url "https://omnilyzer.ai" && echo "✅ Online" || echo "❌ Offline")"
}

# Main script logic
case "${1:-check}" in
    "check")
        check_services
        ;;
    "status")
        show_status
        ;;
    "restart")
        log_message "🔄 Manual restart requested"
        restart_services
        ;;
    "monitor")
        log_message "🚀 Starting continuous monitoring..."
        while true; do
            check_services
            sleep 300  # Check every 5 minutes
        done
        ;;
    *)
        echo "Usage: $0 {check|status|restart|monitor}"
        echo ""
        echo "Commands:"
        echo "  check   - Check service health and restart if needed (default)"
        echo "  status  - Show current status of all services"
        echo "  restart - Force restart all services"
        echo "  monitor - Start continuous monitoring (every 5 minutes)"
        exit 1
        ;;
esac
