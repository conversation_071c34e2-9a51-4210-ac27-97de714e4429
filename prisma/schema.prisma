// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// User model for authentication and authorization
model User {
  id             Int      @id @default(autoincrement())
  email          String   @unique
  name           String
  password       String?  // Optional for OAuth users
  role           String   @default("Guest") // Admin, Developer, Reviewer, Guest
  provider       String?  // For OAuth ("google", "local", etc.)
  providerId     String?  // Provider's user ID
  emailVerified  <PERSON><PERSON><PERSON>  @default(false)
  verifyToken    String?  // Email verification token
  resetToken     String?  // Password reset token
  resetExpiry    DateTime?
  profileImage   String?  // URL to profile image
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  lastLoginAt    DateTime?

  // Relations
  sessions       Session[]
  notifications  Notification[]

  // Advanced Analytics Relations
  abTestAssignments    ABTestAssignment[]
  abTestConversions    ABTestConversion[]
  funnelSessions       FunnelSession[]
  heatMapData          HeatMapData[]
  emailRecipients      EmailRecipient[]
  segmentMemberships   UserSegmentMembership[]
  behaviorEvents       UserBehaviorEvent[]
  profile              UserProfile?

  @@index([email])
}

// Session model for tracking user sessions
model Session {
  id        String   @id @default(cuid())
  userId    Int
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  ipAddress String?
  userAgent String?
  
  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([token])
}

// Notification model for user notifications
model Notification {
  id        Int      @id @default(autoincrement())
  userId    Int
  message   String
  type      String   // info, success, warning, error
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
}

// System Settings model for application configuration
model SystemSetting {
  id        Int      @id @default(autoincrement())
  key       String   @unique
  value     String   @db.Text
  type      String   // string, number, boolean, json
  category  String   // general, security, email, etc.
  isPublic  Boolean  @default(false) // Whether setting is accessible to frontend
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@index([key])
  @@index([category])
}

// Audit Log model for tracking system activities
model AuditLog {
  id        Int      @id @default(autoincrement())
  userId    Int?
  action    String
  resource  String
  resourceId String?
  details   String?  @db.Text
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  
  @@index([userId])
  @@index([action])
  @@index([resource])
}

// Contact Form Submissions model for analytics
model ContactSubmission {
  id          Int      @id @default(autoincrement())
  name        String
  email       String
  subject     String
  message     String   @db.Text
  ipAddress   String?
  userAgent   String?  @db.Text
  source      String?  // referrer, utm_source, etc.
  status      String   @default("new") // new, contacted, converted, spam
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([email])
  @@index([status])
  @@index([createdAt])
}

// Page Views model for analytics
model PageView {
  id          Int      @id @default(autoincrement())
  path        String
  title       String?
  referrer    String?
  userAgent   String?  @db.Text
  ipAddress   String?
  sessionId   String?
  userId      Int?
  duration    Int?     // Time spent on page in seconds
  createdAt   DateTime @default(now())

  @@index([path])
  @@index([sessionId])
  @@index([userId])
  @@index([createdAt])
}

// System Metrics model for performance tracking
model SystemMetric {
  id          Int      @id @default(autoincrement())
  metric      String   // cpu_usage, memory_usage, response_time, etc.
  value       Float
  unit        String   // percentage, ms, bytes, etc.
  category    String   // performance, security, business, etc.
  metadata    String?  @db.Text // JSON string for additional data
  createdAt   DateTime @default(now())

  @@index([metric])
  @@index([category])
  @@index([createdAt])
}

// A/B Testing Framework
model ABTest {
  id          Int      @id @default(autoincrement())
  name        String
  description String?  @db.Text
  status      String   @default("draft") // draft, active, paused, completed
  startDate   DateTime?
  endDate     DateTime?
  variants    ABTestVariant[]
  assignments ABTestAssignment[]
  conversions ABTestConversion[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([status])
  @@index([startDate])
}

model ABTestVariant {
  id          Int      @id @default(autoincrement())
  testId      Int
  name        String
  description String?  @db.Text
  config      String   @db.Text // JSON configuration
  trafficSplit Float   @default(50.0) // Percentage of traffic
  test        ABTest   @relation(fields: [testId], references: [id], onDelete: Cascade)
  assignments ABTestAssignment[]
  conversions ABTestConversion[]
  createdAt   DateTime @default(now())

  @@index([testId])
}

model ABTestAssignment {
  id        Int           @id @default(autoincrement())
  testId    Int
  variantId Int
  userId    Int?
  sessionId String
  ipAddress String?
  userAgent String?       @db.Text
  test      ABTest        @relation(fields: [testId], references: [id], onDelete: Cascade)
  variant   ABTestVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)
  user      User?         @relation(fields: [userId], references: [id])
  createdAt DateTime      @default(now())

  @@unique([testId, sessionId])
  @@index([testId])
  @@index([variantId])
  @@index([sessionId])
}

model ABTestConversion {
  id        Int           @id @default(autoincrement())
  testId    Int
  variantId Int
  userId    Int?
  sessionId String
  goalType  String        // contact_form, signup, purchase, etc.
  goalValue Float?        // Optional value for the conversion
  metadata  String?       @db.Text // JSON metadata
  test      ABTest        @relation(fields: [testId], references: [id], onDelete: Cascade)
  variant   ABTestVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)
  user      User?         @relation(fields: [userId], references: [id])
  createdAt DateTime      @default(now())

  @@index([testId])
  @@index([variantId])
  @@index([goalType])
  @@index([sessionId])
}

// Conversion Funnel Tracking
model ConversionFunnel {
  id          Int      @id @default(autoincrement())
  name        String
  description String?  @db.Text
  steps       ConversionStep[]
  sessions    FunnelSession[]
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model ConversionStep {
  id          Int      @id @default(autoincrement())
  funnelId    Int
  name        String
  description String?  @db.Text
  stepOrder   Int
  pagePattern String   // URL pattern or regex
  eventType   String   // pageview, click, form_submit, etc.
  eventTarget String?  // CSS selector or element identifier
  funnel      ConversionFunnel @relation(fields: [funnelId], references: [id], onDelete: Cascade)
  completions FunnelStepCompletion[]
  createdAt   DateTime @default(now())

  @@index([funnelId])
  @@index([stepOrder])
}

model FunnelSession {
  id          Int      @id @default(autoincrement())
  funnelId    Int
  sessionId   String
  userId      Int?
  startedAt   DateTime @default(now())
  completedAt DateTime?
  currentStep Int      @default(1)
  funnel      ConversionFunnel @relation(fields: [funnelId], references: [id], onDelete: Cascade)
  user        User?    @relation(fields: [userId], references: [id])
  completions FunnelStepCompletion[]

  @@unique([funnelId, sessionId])
  @@index([funnelId])
  @@index([sessionId])
}

model FunnelStepCompletion {
  id          Int      @id @default(autoincrement())
  sessionId   Int
  stepId      Int
  completedAt DateTime @default(now())
  timeSpent   Int?     // Time spent on this step in seconds
  metadata    String?  @db.Text // JSON metadata
  session     FunnelSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  step        ConversionStep @relation(fields: [stepId], references: [id], onDelete: Cascade)

  @@unique([sessionId, stepId])
  @@index([sessionId])
  @@index([stepId])
}

// Heat Map Tracking
model HeatMapData {
  id          Int      @id @default(autoincrement())
  sessionId   String
  userId      Int?
  pageUrl     String
  elementType String   // click, scroll, hover, form_focus
  elementId   String?
  elementClass String?
  elementTag  String?
  xPosition   Int?
  yPosition   Int?
  scrollDepth Int?     // Percentage of page scrolled
  timestamp   DateTime @default(now())
  user        User?    @relation(fields: [userId], references: [id])

  @@index([sessionId])
  @@index([pageUrl])
  @@index([elementType])
  @@index([timestamp])
}

// Email Campaign Analytics
model EmailCampaign {
  id          Int      @id @default(autoincrement())
  name        String
  subject     String
  fromEmail   String
  fromName    String?
  htmlContent String   @db.Text
  textContent String?  @db.Text
  status      String   @default("draft") // draft, scheduled, sending, sent, paused
  scheduledAt DateTime?
  sentAt      DateTime?
  recipients  EmailRecipient[]
  events      EmailEvent[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([status])
  @@index([scheduledAt])
}

model EmailRecipient {
  id         Int      @id @default(autoincrement())
  campaignId Int
  email      String
  name       String?
  userId     Int?
  status     String   @default("pending") // pending, sent, failed, bounced
  sentAt     DateTime?
  campaign   EmailCampaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  user       User?    @relation(fields: [userId], references: [id])
  events     EmailEvent[]
  createdAt  DateTime @default(now())

  @@unique([campaignId, email])
  @@index([campaignId])
  @@index([email])
  @@index([status])
}

model EmailEvent {
  id          Int      @id @default(autoincrement())
  campaignId  Int
  recipientId Int
  eventType   String   // sent, delivered, opened, clicked, bounced, complained, unsubscribed
  eventData   String?  @db.Text // JSON data (link clicked, bounce reason, etc.)
  userAgent   String?  @db.Text
  ipAddress   String?
  timestamp   DateTime @default(now())
  campaign    EmailCampaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  recipient   EmailRecipient @relation(fields: [recipientId], references: [id], onDelete: Cascade)

  @@index([campaignId])
  @@index([recipientId])
  @@index([eventType])
  @@index([timestamp])
}

// User Segmentation and Behavior Analysis
model UserSegment {
  id          Int      @id @default(autoincrement())
  name        String
  description String?  @db.Text
  conditions  String   @db.Text // JSON conditions for segment membership
  isActive    Boolean  @default(true)
  users       UserSegmentMembership[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([isActive])
}

model UserSegmentMembership {
  id        Int         @id @default(autoincrement())
  userId    Int
  segmentId Int
  addedAt   DateTime    @default(now())
  removedAt DateTime?
  user      User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  segment   UserSegment @relation(fields: [segmentId], references: [id], onDelete: Cascade)

  @@unique([userId, segmentId])
  @@index([userId])
  @@index([segmentId])
}

model UserBehaviorEvent {
  id          Int      @id @default(autoincrement())
  userId      Int?
  sessionId   String
  eventType   String   // page_view, click, scroll, form_interaction, etc.
  eventName   String
  properties  String   @db.Text // JSON properties
  pageUrl     String
  referrer    String?
  userAgent   String?  @db.Text
  ipAddress   String?
  timestamp   DateTime @default(now())
  user        User?    @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([sessionId])
  @@index([eventType])
  @@index([eventName])
  @@index([timestamp])
}

// Blog Posts model for AI-generated content
model BlogPost {
  id          Int      @id @default(autoincrement())
  title       String
  slug        String   @unique
  summary     String   @db.Text
  content     String   @db.Text
  author      String   @default("Michael Hansen")
  status      String   @default("draft") // draft, published, archived
  category    String?
  tags        String?  // JSON array of tags
  readTime    Int?     // Estimated read time in minutes
  trendTopic  String?  // The Google trend topic this was based on
  aiPrompt    String?  @db.Text // The prompt used to generate this content
  publishedAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Social media posts related to this blog
  socialPosts BlogSocialPost[]

  @@index([status])
  @@index([publishedAt])
  @@index([slug])
  @@index([category])
}

// Social Media Posts model for tracking blog promotion
model BlogSocialPost {
  id         Int      @id @default(autoincrement())
  blogPostId Int
  platform   String   // linkedin, facebook, twitter
  postId     String?  // Platform's post ID
  content    String   @db.Text
  status     String   @default("pending") // pending, posted, failed
  scheduledAt DateTime?
  postedAt   DateTime?
  error      String?  @db.Text
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  blogPost   BlogPost @relation(fields: [blogPostId], references: [id], onDelete: Cascade)

  @@index([blogPostId])
  @@index([platform])
  @@index([status])
  @@index([scheduledAt])
}

// Blog Generation Jobs model for tracking automated content creation
model BlogGenerationJob {
  id          Int      @id @default(autoincrement())
  status      String   @default("pending") // pending, running, completed, failed
  trendTopic  String?
  trendData   String?  @db.Text // JSON data from trends API
  blogPostId  Int?
  error       String?  @db.Text
  startedAt   DateTime?
  completedAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  blogPost    BlogPost? @relation(fields: [blogPostId], references: [id])

  @@index([status])
  @@index([createdAt])
}

model UserProfile {
  id              Int      @id @default(autoincrement())
  userId          Int      @unique
  totalPageViews  Int      @default(0)
  totalSessions   Int      @default(0)
  avgSessionTime  Int?     // Average session time in seconds
  lastActiveAt    DateTime?
  firstSeenAt     DateTime @default(now())
  preferredDevice String?  // mobile, desktop, tablet
  preferredBrowser String?
  location        String?  // Derived from IP
  timezone        String?
  lifetimeValue   Float    @default(0.0)
  engagementScore Float    @default(0.0)
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  updatedAt       DateTime @updatedAt

  @@index([lastActiveAt])
  @@index([engagementScore])
  @@index([lifetimeValue])
}
